#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
服务器连接测试脚本
"""

import requests
import json
import time

BASE_URL = "https://haoke.kechengmao.top"

def test_server_basic():
    """测试服务器基本连接"""
    print("🌐 测试服务器基本连接...")
    
    try:
        # 测试根域名
        response = requests.get("https://haoke.kechengmao.top/", timeout=10)
        print(f"✅ 根域名访问成功 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 根域名访问失败: {e}")
        return False
    
    return True

def test_api_file():
    """测试API测试文件"""
    print("\n🔧 测试API测试文件...")
    
    try:
        # 测试GET请求
        response = requests.get("https://haoke.kechengmao.top/test_api.php", timeout=10)
        print(f"GET请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API测试文件响应正常")
            print(f"PHP版本: {data['server_info']['php_version']}")
            print(f"HTTPS状态: {data['server_info']['https']}")
            print(f"服务器软件: {data['server_info']['server_software']}")
        else:
            print(f"❌ API测试文件响应异常: {response.text}")
            
    except Exception as e:
        print(f"❌ API测试文件访问失败: {e}")
        return False
    
    try:
        # 测试POST请求
        test_data = {"test": "data", "timestamp": 1234567890}
        response = requests.post(
            "https://haoke.kechengmao.top/test_api.php", 
            json=test_data,
            timeout=10
        )
        print(f"POST请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ POST请求测试成功")
        else:
            print(f"❌ POST请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ POST请求测试失败: {e}")
        return False
    
    return True

def test_verify_api_direct():
    """直接测试验证API"""
    print("\n🔐 直接测试验证API...")
    
    try:
        # 直接访问verify.php文件
        test_data = {
            "app_id": "haoke_downloader",
            "license_key": "test_license_key_for_development_only_2024",
            "device_id": "test_device_12345_valid_length_16chars",
            "timestamp": int(time.time())
        }
        
        response = requests.post(
            "https://haoke.kechengmao.top/api/auth/verify.php",
            json=test_data,
            timeout=10
        )
        
        print(f"验证API状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 验证API响应格式正确")
                return True
            except:
                print("❌ 验证API响应不是有效JSON")
        else:
            print("❌ 验证API返回错误状态码")
            
    except Exception as e:
        print(f"❌ 验证API访问失败: {e}")
    
    return False

def test_debug_api():
    """测试调试API"""
    print("\n🔍 运行详细诊断...")

    try:
        # 先测试GET请求
        response = requests.get(f"{BASE_URL}/debug_api.php", timeout=10)
        print(f"调试API GET状态码: {response.status_code}")

        if response.status_code == 200:
            debug_data = response.json()
            print("✅ 调试信息获取成功")

            # 显示关键信息
            print(f"PHP版本: {debug_data.get('php_version', 'Unknown')}")
            print(f"服务器软件: {debug_data.get('server_software', 'Unknown')}")

            # 检查文件状态
            file_tests = debug_data.get('file_tests', {})
            print("\n📁 文件检查结果:")
            for file_name, status in file_tests.items():
                if isinstance(status, dict):
                    exists = status.get('exists', False)
                    readable = status.get('readable', False)
                    print(f"   {file_name}: {'✅' if exists and readable else '❌'} (存在: {exists}, 可读: {readable})")
                else:
                    print(f"   {file_name}: {'✅' if status else '❌'}")

            # 检查数据库连接
            db_status = debug_data.get('database_connection', 'Unknown')
            print(f"\n🗄️ 数据库连接: {'✅' if db_status == 'Success' else '❌'} ({db_status})")

            # 检查数据库表
            if 'database_tables' in debug_data:
                print("📊 数据库表状态:")
                for table, status in debug_data['database_tables'].items():
                    print(f"   {table}: {status}")

            return True
        else:
            print(f"❌ 调试API访问失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 调试API测试失败: {e}")
        return False

def test_simple_verify():
    """测试简化版验证API"""
    print("\n🧪 测试简化版验证API...")

    # 测试数据
    test_data = {
        'app_id': 'haoke_downloader',
        'license_key': 'test_license_key_for_development_only_2024',
        'device_id': 'test_device_12345_valid_length_16chars',
        'timestamp': int(time.time())
    }

    try:
        response = requests.post(
            f"{BASE_URL}/simple_verify.php",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        print(f"简化验证API状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 简化验证API访问成功")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ 简化验证API返回错误状态码")
            print(f"响应内容: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 简化验证API测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    # 创建一个简单的数据库测试文件内容
    test_db_php = '''<?php
// 简单的数据库连接测试
header('Content-Type: application/json');

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=haoke_kechengmao;charset=utf8mb4",
        "haoke_kechengmao",
        "9Fyc8e9HRXbDbzmX"
    );
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM apps");
    $result = $stmt->fetch();
    
    echo json_encode([
        "success" => true,
        "message" => "数据库连接成功",
        "apps_count" => $result['count']
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => "数据库连接失败: " . $e->getMessage()
    ]);
}
?>'''
    
    print("💡 请在服务器上创建 test_db.php 文件，内容如下：")
    print("-" * 50)
    print(test_db_php)
    print("-" * 50)
    print("然后访问: https://haoke.kechengmao.top/test_db.php")

def main():
    """主测试函数"""
    print("🧪 服务器配置测试")
    print("=" * 50)
    
    # 基本连接测试
    if not test_server_basic():
        print("\n❌ 服务器基本连接失败，请检查域名和SSL配置")
        return
    
    # API文件测试
    if not test_api_file():
        print("\n❌ API测试文件访问失败")
        print("💡 请确保 test_api.php 已上传到网站根目录")
        return
    
    # 运行详细诊断
    test_debug_api()

    # 测试简化版验证API
    test_simple_verify()

    # 验证API测试
    if not test_verify_api_direct():
        print("\n❌ 验证API访问失败")
        print("💡 可能的问题：")
        print("   1. verify.php 文件未正确上传")
        print("   2. includes 目录下的文件缺失")
        print("   3. 数据库连接配置错误")
        print("   4. 文件权限问题")

    # 数据库测试提示
    test_database_connection()
    
    print("\n📋 检查清单：")
    print("1. ✅ 上传 test_api.php 到网站根目录")
    print("2. ⬜ 上传所有 API 文件到正确位置")
    print("3. ⬜ 设置正确的文件权限")
    print("4. ⬜ 确认数据库配置正确")
    print("5. ⬜ 检查 .htaccess 重写规则")

if __name__ == "__main__":
    main()
