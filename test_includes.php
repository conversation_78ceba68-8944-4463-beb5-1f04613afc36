<?php
/**
 * 测试includes文件是否有问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义框架常量
define('SECURITY_FRAMEWORK', true);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

$test_results = [
    'timestamp' => time(),
    'tests' => []
];

// 测试每个文件的包含
$files_to_test = [
    'config.php' => 'includes/config.php',
    'database.php' => 'includes/database.php',
    'security.php' => 'includes/security.php',
    'license.php' => 'includes/license.php',
    'logger.php' => 'includes/logger.php'
];

foreach ($files_to_test as $name => $path) {
    $test_results['tests'][$name] = [
        'exists' => file_exists($path),
        'readable' => file_exists($path) ? is_readable($path) : false,
        'included' => false,
        'error' => null
    ];
    
    if (file_exists($path) && is_readable($path)) {
        try {
            require_once $path;
            $test_results['tests'][$name]['included'] = true;
        } catch (Exception $e) {
            $test_results['tests'][$name]['error'] = 'Exception: ' . $e->getMessage();
        } catch (ParseError $e) {
            $test_results['tests'][$name]['error'] = 'Parse Error: ' . $e->getMessage();
        } catch (Error $e) {
            $test_results['tests'][$name]['error'] = 'Fatal Error: ' . $e->getMessage();
        }
    }
}

// 测试函数是否存在
$test_results['functions'] = [];
$functions_to_check = ['getClientIP', 'writeLog', 'errorResponse', 'successResponse'];
foreach ($functions_to_check as $func) {
    $test_results['functions'][$func] = function_exists($func);
}

// 测试类是否存在
$test_results['classes'] = [];
$classes_to_check = ['Database', 'Security', 'LicenseManager', 'Logger'];
foreach ($classes_to_check as $class) {
    $test_results['classes'][$class] = class_exists($class);
}

// 测试数据库连接
if (class_exists('Database')) {
    try {
        $db = Database::getInstance();
        $test_results['database'] = 'Connected successfully';
    } catch (Exception $e) {
        $test_results['database'] = 'Connection failed: ' . $e->getMessage();
    }
} else {
    $test_results['database'] = 'Database class not found';
}

// 测试日志写入
if (function_exists('writeLog')) {
    try {
        writeLog('INFO', 'Test log entry from test_includes.php');
        $test_results['logging'] = 'Success';
    } catch (Exception $e) {
        $test_results['logging'] = 'Failed: ' . $e->getMessage();
    }
} else {
    $test_results['logging'] = 'writeLog function not found';
}

echo json_encode($test_results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
