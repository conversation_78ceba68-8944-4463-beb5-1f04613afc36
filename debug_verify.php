<?php
/**
 * 调试版验证接口 - 用于找出具体错误
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义框架常量
define('SECURITY_FRAMEWORK', true);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$debug_info = [
    'timestamp' => time(),
    'method' => $_SERVER['REQUEST_METHOD'],
    'step' => 'start',
    'errors' => []
];

try {
    $debug_info['step'] = 'checking_method';
    
    // 只允许POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $debug_info['errors'][] = 'Method not POST: ' . $_SERVER['REQUEST_METHOD'];
        echo json_encode($debug_info);
        exit;
    }
    
    $debug_info['step'] = 'including_files';
    
    // 尝试引入核心文件
    $files_to_include = [
        'config.php' => '../../includes/config.php',
        'database.php' => '../../includes/database.php',
        'security.php' => '../../includes/security.php',
        'license.php' => '../../includes/license.php',
        'logger.php' => '../../includes/logger.php'
    ];
    
    $debug_info['file_inclusion'] = [];
    
    foreach ($files_to_include as $name => $path) {
        try {
            if (!file_exists($path)) {
                $debug_info['file_inclusion'][$name] = 'File not found: ' . $path;
                continue;
            }
            
            if (!is_readable($path)) {
                $debug_info['file_inclusion'][$name] = 'File not readable: ' . $path;
                continue;
            }
            
            require_once $path;
            $debug_info['file_inclusion'][$name] = 'Success';
            
        } catch (Exception $e) {
            $debug_info['file_inclusion'][$name] = 'Error: ' . $e->getMessage();
        } catch (ParseError $e) {
            $debug_info['file_inclusion'][$name] = 'Parse Error: ' . $e->getMessage();
        } catch (Error $e) {
            $debug_info['file_inclusion'][$name] = 'Fatal Error: ' . $e->getMessage();
        }
    }
    
    $debug_info['step'] = 'getting_input';
    
    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    // 如果JSON解析失败，尝试解析表单数据
    if ($input === null) {
        $input = $_POST;
    }
    
    $debug_info['input_data'] = [
        'raw_input_length' => strlen($rawInput),
        'json_decode_success' => $input !== null,
        'post_data_count' => count($_POST),
        'final_input_count' => count($input ?? [])
    ];
    
    if (empty($input)) {
        $debug_info['errors'][] = 'Empty input data';
        echo json_encode($debug_info);
        exit;
    }
    
    $debug_info['step'] = 'validating_params';
    
    // 验证必需参数
    $requiredParams = ['app_id', 'license_key', 'device_id', 'timestamp'];
    $missingParams = [];
    
    foreach ($requiredParams as $param) {
        if (!isset($input[$param]) || $input[$param] === '') {
            $missingParams[] = $param;
        }
    }
    
    if (!empty($missingParams)) {
        $debug_info['errors'][] = 'Missing parameters: ' . implode(', ', $missingParams);
        echo json_encode($debug_info);
        exit;
    }
    
    $debug_info['step'] = 'checking_functions';
    
    // 检查必需的函数是否存在
    $required_functions = [
        'getClientIP',
        'errorResponse',
        'successResponse',
        'writeLog'
    ];
    
    $debug_info['function_check'] = [];
    foreach ($required_functions as $func) {
        $debug_info['function_check'][$func] = function_exists($func);
    }
    
    $debug_info['step'] = 'checking_classes';
    
    // 检查必需的类是否存在
    $required_classes = [
        'Security',
        'Database',
        'LicenseManager'
    ];
    
    $debug_info['class_check'] = [];
    foreach ($required_classes as $class) {
        $debug_info['class_check'][$class] = class_exists($class);
    }
    
    $debug_info['step'] = 'testing_client_ip';
    
    // 测试获取客户端IP
    if (function_exists('getClientIP')) {
        try {
            $clientIP = getClientIP();
            $debug_info['client_ip'] = $clientIP;
        } catch (Exception $e) {
            $debug_info['errors'][] = 'getClientIP error: ' . $e->getMessage();
        }
    }
    
    $debug_info['step'] = 'testing_security_class';
    
    // 测试Security类
    if (class_exists('Security')) {
        try {
            $sanitized = Security::sanitizeInput($input);
            $debug_info['security_sanitize'] = 'Success';
        } catch (Exception $e) {
            $debug_info['errors'][] = 'Security::sanitizeInput error: ' . $e->getMessage();
        }
    }
    
    $debug_info['step'] = 'testing_database';
    
    // 测试数据库连接
    if (class_exists('Database')) {
        try {
            $db = Database::getInstance();
            $debug_info['database_connection'] = 'Success';
            
            // 测试查询应用
            $app = $db->fetchOne(
                "SELECT * FROM apps WHERE app_id = ? AND status = 1",
                [$input['app_id']]
            );
            $debug_info['app_query'] = $app ? 'Found' : 'Not found';
            
        } catch (Exception $e) {
            $debug_info['errors'][] = 'Database error: ' . $e->getMessage();
        }
    }
    
    $debug_info['step'] = 'testing_license_manager';
    
    // 测试LicenseManager
    if (class_exists('LicenseManager')) {
        try {
            $licenseManager = new LicenseManager();
            $debug_info['license_manager'] = 'Created successfully';
        } catch (Exception $e) {
            $debug_info['errors'][] = 'LicenseManager error: ' . $e->getMessage();
        }
    }
    
    $debug_info['step'] = 'completed';
    $debug_info['success'] = true;
    
} catch (Exception $e) {
    $debug_info['errors'][] = 'Exception: ' . $e->getMessage();
    $debug_info['exception_details'] = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];
} catch (Error $e) {
    $debug_info['errors'][] = 'Fatal Error: ' . $e->getMessage();
    $debug_info['error_details'] = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];
}

// 输出调试信息
echo json_encode($debug_info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
