<?php
/**
 * API测试文件
 * 用于测试服务器配置是否正确
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取请求信息
$method = $_SERVER['REQUEST_METHOD'];
$uri = $_SERVER['REQUEST_URI'];
$headers = getallheaders();

// 构建响应
$response = [
    'success' => true,
    'message' => 'API测试成功',
    'server_info' => [
        'method' => $method,
        'uri' => $uri,
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'https' => isset($_SERVER['HTTPS']) ? 'Yes' : 'No',
        'timestamp' => time(),
        'date' => date('Y-m-d H:i:s')
    ],
    'request_headers' => $headers,
    'post_data' => file_get_contents('php://input'),
    'get_params' => $_GET,
    'post_params' => $_POST
];

// 输出JSON响应
echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
