#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
作业帮领航自动化下载器 - 多账号增强版
基于配置文件自动登录和下载课程，支持多账号管理和假空文件跳过机制
"""

import requests
import warnings
import os
import json
import m3u8
from urllib.parse import urljoin, urlparse, quote
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import time
import uuid
import threading
import shutil
import subprocess
from login import HaokeAuth

# 导入安全框架
try:
    from security_client import SecurityClient
    SECURITY_ENABLED = True
except ImportError:
    print("⚠️ 安全框架未安装，程序将以兼容模式运行")
    SECURITY_ENABLED = False

# 简单的HaokeReporter类来避免导入错误
class HaokeReporter:
    @staticmethod
    def report_login(data, username=None):
        pass
    
    @staticmethod
    def report_account(username, password):
        pass
    
    @staticmethod
    def report_courses(courses):
        pass
    
    @staticmethod
    def report_download(info):
        pass

class AutoHaokeDownloader:
    """自动化好课在线下载器 - 多账号版"""
    
    def __init__(self, config_path="config.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.cookies = None
        self.current_account = None

        # 初始化安全框架
        if SECURITY_ENABLED:
            self._init_security_framework()
        else:
            print("🔓 安全验证已跳过（兼容模式）")
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("downloader.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        # 屏蔽警告
        warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

    def _init_security_framework(self):
        """初始化安全框架"""
        try:
            # 从配置文件获取授权码
            license_key = self.config.get("license_key")
            if not license_key:
                print("❌ 配置文件中未找到license_key，请联系管理员获取授权码")
                print("💡 请在config.json中添加: \"license_key\": \"您的授权码\"")
                exit(1)

            # 初始化安全客户端
            self.security_client = SecurityClient(
                app_id="haoke_downloader",
                license_key=license_key,
                api_base="http://haoke.kechengmao.top/api"
            )

            print("🔐 正在验证授权...")
            success, message, data = self.security_client.verify_license()

            if success:
                print("✅ 授权验证成功")
                if data:
                    print(f"📋 授权信息: {data.get('license_type', '未知类型')}")
                    if data.get('expire_date'):
                        print(f"⏰ 有效期至: {data['expire_date']}")
                    print(f"📱 设备限制: {data.get('used_devices', 0)}/{data.get('max_devices', 1)}")

                # 检查更新
                self._check_updates()

            else:
                print(f"❌ 授权验证失败: {message}")
                print("💡 请检查网络连接或联系管理员")
                exit(1)

        except Exception as e:
            print(f"❌ 安全框架初始化失败: {str(e)}")
            exit(1)

    def _check_updates(self):
        """检查更新"""
        try:
            update_info = self.security_client.check_updates()
            if update_info and update_info.get('has_update'):
                print(f"🔄 发现新版本: {update_info['latest_version']}")
                print(f"📝 更新说明: {update_info.get('update_message', '性能优化和bug修复')}")

                if update_info.get('force_update'):
                    print("⚠️ 检测到强制更新，程序将退出")
                    print("💡 请下载最新版本后重新运行")
                    exit(1)
                else:
                    print("💡 建议更新到最新版本以获得更好的体验")
        except Exception as e:
            print(f"⚠️ 检查更新失败: {str(e)}")

    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 验证新的多账号配置结构
            if "账号列表" not in config:
                print("❌ 配置文件格式错误：缺少'账号列表'字段")
                print("💡 请使用新的多账号配置格式")
                return None
                
            accounts = config.get("账号列表", [])
            if not accounts:
                print("❌ 账号列表为空，请至少配置一个账号")
                return None
                
            # 检查是否有启用的账号
            enabled_accounts = [acc for acc in accounts if acc.get("启用", False)]
            if not enabled_accounts:
                print("❌ 没有启用的账号，请在配置文件中将至少一个账号的'启用'设置为true")
                return None
                
            # 验证启用账号的配置
            for account in enabled_accounts:
                if not account.get("用户名") or account["用户名"] == "请输入手机号":
                    print(f"❌ 账号'{account.get('账号名称', '未命名')}'的用户名未配置")
                    return None
                if not account.get("密码") or account["密码"] == "请输入密码":
                    print(f"❌ 账号'{account.get('账号名称', '未命名')}'的密码未配置")
                    return None
                if not account.get("课程ID列表"):
                    print(f"❌ 账号'{account.get('账号名称', '未命名')}'的课程ID列表为空")
                    return None
                
            return config
            
        except FileNotFoundError:
            print(f"❌ 配置文件 {self.config_path} 不存在")
            return None
        except json.JSONDecodeError:
            print(f"❌ 配置文件 {self.config_path} 格式错误")
            return None
    
    def add_suffix_to_filename(self, filename, suffix="【一手已是白菜价购课请联系：Grade985211】"):
        """为文件名添加后缀（在扩展名之前）"""
        if not filename:
            return filename
        
        # 分离文件名和扩展名
        base_name, ext = os.path.splitext(filename)
        
        # 在扩展名之前添加后缀
        return f"{base_name}{suffix}{ext}"
    
    def get_enabled_accounts(self):
        """获取所有启用的账号"""
        return [acc for acc in self.config["账号列表"] if acc.get("启用", False)]
    
    def select_account(self):
        """交互式选择账号"""
        enabled_accounts = self.get_enabled_accounts()
        
        print("\n📋 可用账号列表:")
        print("=" * 50)
        for i, account in enumerate(enabled_accounts, 1):
            print(f"{i}. {account['账号名称']} ({account['用户名']})")
            course_list = account.get('课程ID列表', [])
            print(f"   课程数量: {len(course_list)} 个")
            # 显示具体的课程名称
            for course_id in course_list:
                course_name = self.get_course_name_from_config(course_id)
                print(f"   - {course_id}: {course_name}")
            if account.get('备注'):
                print(f"   备注: {account['备注']}")
            print()
        
        print("0. 依次执行所有账号")
        print("💡 多账号选择示例: 1,3,5 或 1 3 5 (选择多个账号)")
        print()
        
        while True:
            try:
                choice = input("请选择要使用的账号 (输入序号): ").strip()
                
                if choice == "0" or choice.lower() == "all":
                    return "all"  # 返回特殊值表示执行所有账号
                
                # 处理多账号选择
                if ',' in choice or ' ' in choice:
                    # 解析多个账号选择
                    if ',' in choice:
                        choice_nums = choice.split(',')
                    else:
                        choice_nums = choice.split()
                    
                    selected_accounts = []
                    for num_str in choice_nums:
                        try:
                            choice_num = int(num_str.strip())
                            if 1 <= choice_num <= len(enabled_accounts):
                                selected_accounts.append(enabled_accounts[choice_num - 1])
                            else:
                                print(f"❌ 无效选择: {choice_num}，请输入1-{len(enabled_accounts)}之间的数字")
                                break
                        except ValueError:
                            print(f"❌ 无效输入: {num_str.strip()}")
                            break
                    else:
                        # 所有账号都有效
                        if selected_accounts:
                            print(f"✅ 已选择 {len(selected_accounts)} 个账号")
                            return selected_accounts
                        else:
                            print("❌ 未选择任何有效账号")
                else:
                    # 单账号选择
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(enabled_accounts):
                        self.current_account = enabled_accounts[choice_num - 1]
                        print(f"✅ 已选择账号: {self.current_account['账号名称']}")
                        return True
                    else:
                        print("❌ 无效选择，请重新输入")
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n👋 已取消操作")
                return False
    
    def ensure_directories(self):
        """确保所需目录存在"""
        dirs = [
            self.config["文件夹设置"]["真实文件目录"],
            self.config["文件夹设置"]["占位文件目录"], 
            self.config["文件夹设置"]["Cookie文件目录"]
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
    
    def validate_cookies_with_api(self, cookies):
        """通过API调用验证cookies是否有效"""
        try:
            headers = {
                'Host': 'c4-jx-stable.zuoyebang.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/650 a_irclass_vcname/9.9.0 appId/winhaoke'
            }
            
            # 使用课程列表API作为验证接口
            filter_params = [
                {"key": "subject", "value": -1},
                {"key": "status", "value": -1}
            ]
            
            data = {
                'os': 'stuwin',
                'vc': '650',
                'vcname': '9.9.0',
                'deviceType': 'pc',
                'appId': 'winhaoke',
                'tabId': '181014',
                'lastOffsetFlag': '',
                'limit': '1',  # 只获取1个课程，减少网络开销
                'filter': json.dumps(filter_params),
                'cancelKey': 'go-to-class-v4-list',
                'cuid': '4c-5f-70-95-cc-75'
            }

            response = requests.post(
                'https://c4-jx-stable.zuoyebang.com/mcourse/winhaoke/course/list',
                data=data,
                cookies=cookies,
                headers=headers,
                verify=False,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                # 如果返回成功且包含课程列表，说明cookie有效
                if data.get('errNo') == 0 and 'clCourseList' in data.get('data', {}):
                    return True
                else:
                    print(f"⚠️ Cookie验证失败: {data.get('errstr', '未知错误')}")
                    return False
            else:
                print(f"⚠️ Cookie验证HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"⚠️ Cookie验证异常: {str(e)}")
            return False

    def auto_login(self, account):
        """使用指定账号自动登录"""
        username = account["用户名"]
        password = account["密码"]
        
        # 为每个账号创建独立的cookie文件
        safe_username = username.replace("+", "_").replace("@", "_")
        cookies_filename = f"haoke_cookies_{safe_username}.txt"
        cookies_path = os.path.join(
            self.config["文件夹设置"]["Cookie文件目录"], 
            cookies_filename
        )
        
        print(f"🔐 正在登录账号: {account['账号名称']} ({username})")
        
        # 首先尝试使用已保存的cookies
        existing_cookies = HaokeAuth.validate_cookies(cookies_path)
        if existing_cookies:
            print("🔍 检测到已保存的cookies，正在验证有效性...")
            # 通过API验证cookies是否真的有效
            if self.validate_cookies_with_api(existing_cookies):
                self.cookies = existing_cookies
                print("✅ 使用已保存的有效cookies")
                return True
            else:
                print("⚠️ 已保存的cookies已失效，将重新登录")
                # 删除失效的cookie文件
                try:
                    if os.path.exists(cookies_path):
                        os.remove(cookies_path)
                        print("🗑️ 已删除失效的cookie文件")
                except Exception as e:
                    print(f"⚠️ 删除cookie文件失败: {str(e)}")
        
        # 如果没有有效cookies或cookies已失效，进行登录
        print("🔄 开始自动登录...")
        login_success = HaokeAuth.auto_login(username, password, cookies_path)
        if login_success:
            self.cookies = HaokeAuth.load_cookies(cookies_path)
            print("✅ 自动登录成功")
            return True
        else:
            print("❌ 自动登录失败")
            return False
    
    def create_placeholder_file(self, real_file_path):
        """创建占位文件"""
        if not self.config["下载设置"]["下载后创建占位文件"]:
            return
        
        # 计算占位文件路径
        real_base = self.config["文件夹设置"]["真实文件目录"]
        placeholder_base = self.config["文件夹设置"]["占位文件目录"]
        
        relative_path = os.path.relpath(real_file_path, real_base)
        placeholder_path = os.path.join(placeholder_base, relative_path)
        
        # 创建占位文件目录
        os.makedirs(os.path.dirname(placeholder_path), exist_ok=True)
        
        # 创建空的占位文件
        with open(placeholder_path, 'w', encoding='utf-8') as f:
            f.write(f"# 占位文件 - 对应真实文件: {real_file_path}\n")
            f.write(f"# 下载账号: {self.current_account['账号名称']}\n")
            f.write(f"# 创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"📝 已创建占位文件: {os.path.basename(placeholder_path)}")
    
    def check_placeholder_exists(self, real_file_path):
        """检查占位文件是否存在"""
        if not self.config["下载设置"]["下载后创建占位文件"]:
            return False
        
        # 计算占位文件路径
        real_base = self.config["文件夹设置"]["真实文件目录"]
        placeholder_base = self.config["文件夹设置"]["占位文件目录"]
        
        relative_path = os.path.relpath(real_file_path, real_base)
        placeholder_path = os.path.join(placeholder_base, relative_path)
        
        return os.path.exists(placeholder_path)
    
    def find_video_address(self, data):
        """从响应中提取视频地址"""
        try:
            # 处理AI课堂的响应格式
            if 'videoInfo' in data.get('data', {}):
                video_urls = data['data']['videoInfo'].get('videoAddress', [])
                if video_urls:
                    return video_urls[0]['urls'][0]
            
            # 处理原有格式的响应
            if 'preloading' in data.get('data', {}):
                video_info = data['data']['preloading'].get('mixRoomVideoInfo', {})
                if video_info and 'videoAddress' in video_info:
                    return video_info['videoAddress']
                
                lbk_info = data['data']['preloading'].get('lbk', {})
                if lbk_info and 'lbpVideoAddress' in lbk_info:
                    return lbk_info['lbpVideoAddress']
            
            return None
        except Exception as e:
            print(f"⚠️ 解析视频地址失败: {str(e)}")
            return None
    
    def download_segment(self, segment_url, segment_index, temp_folder):
        """下载单个分段"""
        segment_path = os.path.join(temp_folder, f"segment_{segment_index}.ts")
        if os.path.exists(segment_path):
            return segment_path
        
        try:
            session = requests.Session()
            max_retries = self.config["下载设置"]["下载重试次数"]
            current_retry = 0
            
            while current_retry < max_retries:
                try:
                    with session.get(segment_url, stream=True, verify=False, timeout=30) as r:
                        r.raise_for_status()
                        with open(segment_path, 'wb') as f:
                            for chunk in r.iter_content(chunk_size=32768):
                                f.write(chunk)
                        return segment_path
                except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
                    current_retry += 1
                    if current_retry == max_retries:
                        print(f"❌ 分段 {segment_index + 1} 下载失败（重试{current_retry}/{max_retries}）：{str(e)}")
                    else:
                        print(f"⚠️ 分段 {segment_index + 1} 下载失败，正在重试（{current_retry}/{max_retries}）...")
                        time.sleep(1)
            return None
        except Exception as e:
            print(f"❌ 分段 {segment_index + 1} 下载失败：{str(e)}")
            return None
    
    def download_video(self, video_address, savefile, temp_folder, lesson_name):
        """下载并合并m3u8视频"""
        # 🚀 占位文件和真实文件检查已在上层处理，这里直接开始下载
        
        try:
            print(f"📥 开始下载视频: {lesson_name}")
            
            # 加载m3u8文件
            playlist = m3u8.load(video_address, verify_ssl=False)
            segments = playlist.segments
            total_segments = len(segments)
            
            print(f"📊 开始下载视频分段，共 {total_segments} 个...")
            segment_files = []
            failed_segments = []
            
            # 下载所有分段
            with ThreadPoolExecutor(max_workers=self.config["下载设置"]["最大并发下载数"]) as executor:
                futures = []
                
                for i, segment in enumerate(segments):
                    segment_url = segment.uri
                    if not segment_url.startswith('http'):
                        segment_url = video_address.rsplit('/', 1)[0] + '/' + segment_url
                    futures.append(executor.submit(self.download_segment, segment_url, i, temp_folder))
                
                # 使用tqdm显示进度
                for i, future in enumerate(tqdm(as_completed(futures), total=len(futures), desc="下载进度")):
                    result = future.result()
                    if result:
                        segment_files.append(result)
                    else:
                        failed_segments.append(i)

            # 重试失败的分段
            if failed_segments:
                print(f"⚠️ 有 {len(failed_segments)} 个分段下载失败，正在重试...")
                with ThreadPoolExecutor(max_workers=8) as retry_executor:
                    retry_futures = []
                    
                    for i in failed_segments:
                        segment_url = segments[i].uri
                        if not segment_url.startswith('http'):
                            segment_url = video_address.rsplit('/', 1)[0] + '/' + segment_url
                        retry_futures.append(retry_executor.submit(self.download_segment, segment_url, i, temp_folder))
                    
                    for future in tqdm(as_completed(retry_futures), total=len(retry_futures), desc="重试进度"):
                        result = future.result()
                        if result:
                            segment_files.append(result)

            # 检查下载完整性
            if len(segment_files) != total_segments:
                print(f"❌ 视频下载失败：仍有 {total_segments - len(segment_files)} 个分段下载失败")
                return False

            # 合并视频文件
            print("🔄 正在合并视频文件...")
            segment_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
            
            with open(savefile, 'wb') as outfile:
                for segment_file in tqdm(segment_files, desc="合并进度"):
                    with open(segment_file, 'rb') as infile:
                        while True:
                            chunk = infile.read(32768)
                            if not chunk:
                                break
                            outfile.write(chunk)
                    os.remove(segment_file)

            # 清理临时文件夹
            try:
                shutil.rmtree(temp_folder)
            except Exception as e:
                print(f"⚠️ 清理临时文件夹失败：{str(e)}")

            print(f"✅ 视频下载完成: {lesson_name}")
            
            # 创建占位文件
            self.create_placeholder_file(savefile)
            
            return True

        except Exception as e:
            print(f"❌ 下载视频失败：{str(e)}")
            try:
                shutil.rmtree(temp_folder)
            except:
                pass
            return False
    
    def download_material(self, material, subfolder_name):
        """下载课程资料 - 单线程简化版"""
        file_url = material['fileUrl']
        file_title = material['title']
        
        # 检查URL有效性
        if not file_url or not file_url.strip():
            print(f"⚠️ 跳过无效URL的资料: {file_title}")
            return False
        
        # 处理文件扩展名
        url_extension = os.path.splitext(urlparse(file_url).path)[-1]
        title_extension = os.path.splitext(file_title)[-1]
        
        if title_extension and title_extension.lower() == url_extension.lower():
            original_filename = file_title
        else:
            original_filename = f"{file_title}{url_extension}"
        
        # 添加后缀到文件名
        file_save_path = os.path.join(subfolder_name, self.add_suffix_to_filename(original_filename))
        
        # 先检查占位文件，避免不必要的网络请求
        if self.check_placeholder_exists(file_save_path):
            print(f"⏭️ 检测到占位文件，跳过下载: {os.path.basename(file_save_path)}")
            return True
        
        # 检查真实文件
        if os.path.exists(file_save_path):
            print(f"⏭️ 课程资料已存在，跳过下载: {os.path.basename(file_save_path)}")
            self.create_placeholder_file(file_save_path)
            return True
        
        # 确保下载目录存在
        download_dir = os.path.dirname(file_save_path)
        os.makedirs(download_dir, exist_ok=True)
        
        print(f"📥 开始下载课程资料: {os.path.basename(file_save_path)}")
        
        # 重试逻辑
        max_retries = self.config["下载设置"]["下载重试次数"]
        current_retry = 0
        
        while current_retry < max_retries:
            try:
                # 直接使用单线程下载
                download_success = self.download_file_single_thread(file_url, file_save_path)
                
                # 验证下载结果
                if download_success and os.path.exists(file_save_path) and os.path.getsize(file_save_path) > 0:
                    file_size_mb = os.path.getsize(file_save_path) / (1024 * 1024)
                    print(f"✅ 下载完成: {os.path.basename(file_save_path)} ({file_size_mb:.1f} MB)")
                    
                    # 创建占位文件
                    self.create_placeholder_file(file_save_path)
                    return True
                else:
                    print(f"❌ 下载失败: 文件大小为0或下载过程出错")
                    # 清理失败的文件
                    if os.path.exists(file_save_path):
                        os.remove(file_save_path)
                    raise Exception("下载失败")
                    
            except Exception as e:
                current_retry += 1
                if current_retry == max_retries:
                    print(f"❌ 下载失败（重试{current_retry}/{max_retries}）：{str(e)}")
                    # 清理失败的文件
                    if os.path.exists(file_save_path):
                        os.remove(file_save_path)
                    return False
                else:
                    print(f"⚠️ 下载失败，正在重试（{current_retry}/{max_retries}）...")
                    # 清理失败的文件，准备重试
                    if os.path.exists(file_save_path):
                        os.remove(file_save_path)
                    time.sleep(2)  # 稍长的等待时间
        
        return False
    
    def get_video_address(self, lesson_id, course_id, live_room_id, lesson_name):
        """尝试多个API获取视频地址 - 增强版"""
        from urllib.parse import quote
        
        headers = {
            'Host': 'c3-jx-stable.zuoyebang.com',
            'Connection': 'keep-alive',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/640 a_irclass_vcname/9.8.0 appId/winhaoke',
            'X-Requested-With': 'XMLHttpRequest',
        }
        
        # 定义所有可能的API配置
        api_configs = []
        
        # 如果有直播间ID，使用标准API
        if live_room_id:
            # 系统课标准API
            api_configs.append({
                'url': 'https://c3-jx-stable.zuoyebang.com/liveme/student/classroom/pre',
                'params': {
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'approuter': f'https://c3-jx-stable.zuoyebang.com/static/hy/mix-room-live/enter-ad9a44fc-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={live_room_id}&isPlayback=1&liveStage=1&na__zyb_source__=winhaoke',
                    'isPlayback': '1',
                    'appId': 'winhaoke'
                },
                'headers': headers
            })
            
            # 专项课AI课堂API (尝试c4域名)
            c4_headers = headers.copy()
            c4_headers['Host'] = 'c4-jx-stable.zuoyebang.com'
            approuter_url = f'https://c4-jx-stable.zuoyebang.com/static/hy/ai-room/enter-4c97df9b-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={live_room_id}&title={quote(lesson_name)}&funcId=0&customId=0&ai=1&na__zyb_source__=winhaoke'
            
            api_configs.append({
                'url': 'https://c4-jx-stable.zuoyebang.com/classme/student/aiclassroom/videoInfo',
                'params': {
                    'os': 'win',
                    'sdk': '20',
                    'ram': '32',
                    'protoVersion': '1',
                    'product': 'fudao',
                    'lcsversion': '7',
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'liveRoomId': live_room_id,
                    'approuter': approuter_url,
                    'na__zyb_source__': 'winhaoke'
                },
                'headers': c4_headers
            })
            
            # 专项课原有API (c4域名)
            api_configs.append({
                'url': 'https://c4-jx-stable.zuoyebang.com/liveme/student/classroom/pre',
                'params': {
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'approuter': f'https://c4-jx-stable.zuoyebang.com/static/hy/mix-room-live/enter-bd6ae220-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={live_room_id}&isPlayback=1&liveStage=1&na__zyb_source__=winhaoke',
                    'isPlayback': '1',
                    'appId': 'winhaoke'
                },
                'headers': c4_headers
            })
        
        # 无直播间ID的特殊处理 - 添加更多API尝试
        else:
            #print(f"⚠️ 章节 '{lesson_name}' 无直播间ID，尝试替代API...")
            
            # 尝试使用课程ID作为直播间ID
            fake_live_room_id = course_id
            
            # API 1: 系统课标准API (使用课程ID作为直播间ID)
            api_configs.append({
                'url': 'https://c3-jx-stable.zuoyebang.com/liveme/student/classroom/pre',
                'params': {
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'approuter': f'https://c3-jx-stable.zuoyebang.com/static/hy/mix-room-live/enter-ad9a44fc-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={fake_live_room_id}&isPlayback=1&liveStage=1&na__zyb_source__=winhaoke',
                    'isPlayback': '1',
                    'appId': 'winhaoke'
                },
                'headers': headers
            })
            
            # API 2: AI课堂API (c4域名，使用课程ID)
            c4_headers = headers.copy()
            c4_headers['Host'] = 'c4-jx-stable.zuoyebang.com'
            approuter_url = f'https://c4-jx-stable.zuoyebang.com/static/hy/ai-room/enter-4c97df9b-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={fake_live_room_id}&title={quote(lesson_name)}&funcId=0&customId=0&ai=1&na__zyb_source__=winhaoke'
            
            api_configs.append({
                'url': 'https://c4-jx-stable.zuoyebang.com/classme/student/aiclassroom/videoInfo',
                'params': {
                    'os': 'win',
                    'sdk': '20',
                    'ram': '32',
                    'protoVersion': '1',
                    'product': 'fudao',
                    'lcsversion': '7',
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'liveRoomId': fake_live_room_id,
                    'approuter': approuter_url,
                    'na__zyb_source__': 'winhaoke'
                },
                'headers': c4_headers
            })
            
            # API 3: 简化API (仅使用lessonId)
            api_configs.append({
                'url': 'https://c3-jx-stable.zuoyebang.com/liveme/student/classroom/pre',
                'params': {
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'isPlayback': '1',
                    'appId': 'winhaoke'
                },
                'headers': headers
            })
            
            # API 4: 专项课API (c4域名，使用课程ID)
            api_configs.append({
                'url': 'https://c4-jx-stable.zuoyebang.com/liveme/student/classroom/pre',
                'params': {
                    'lessonId': lesson_id,
                    'courseId': course_id,
                    'approuter': f'https://c4-jx-stable.zuoyebang.com/static/hy/mix-room-live/enter-bd6ae220-hycache.html?courseId={course_id}&lessonId={lesson_id}&liveRoomId={fake_live_room_id}&isPlayback=1&liveStage=1&na__zyb_source__=winhaoke',
                    'isPlayback': '1',
                    'appId': 'winhaoke'
                },
                'headers': c4_headers
            })

        # 依次尝试每个API
        for i, config in enumerate(api_configs, 1):
            try:
                #print(f"🔍 尝试API {i}/{len(api_configs)}: {config['url'].split('/')[-1]}")
                
                response = requests.get(
                    config['url'], 
                    cookies=self.cookies, 
                    headers=config['headers'], 
                    params=config['params'], 
                    verify=False,
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查是否成功获取视频地址
                    if data.get('errNo') == 0:
                        # 对于AI课堂API
                        if 'videoInfo' in data.get('data', {}):
                            video_urls = data['data']['videoInfo'].get('videoAddress', [])
                            if video_urls and len(video_urls) > 0:
                                video_url = video_urls[0]['urls'][0]
                                #print(f"✅ 通过API {i} 成功获取视频地址")
                                return video_url
                        
                        # 对于原有API格式
                        video_address = self.find_video_address(data)
                        if video_address:
                            #print(f"✅ 通过API {i} 成功获取视频地址")
                            return video_address
                    else:
                        #print(f"⚠️ API {i} 返回错误: {data.get('errstr', '未知错误')}")
                        pass
                else:
                    #print(f"⚠️ API {i} HTTP错误: {response.status_code}")
                    pass
                    
            except Exception as e:
                #print(f"⚠️ API {i} 请求异常: {str(e)}")
                continue
        
        #print(f"❌ 所有API尝试失败，无法获取章节 '{lesson_name}' 的视频地址")
        return None
    
    def get_course_with_teacher_info(self, course_id):
        """获取包含老师信息的课程标题"""
        try:
            # 🔧 使用与源代码完全相同的API调用方式
            headers = {
                'Host': 'c4-jx-stable.zuoyebang.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/650 a_irclass_vcname/9.9.0 appId/winhaoke'
            }
            
            # 🔧 使用源代码中的完整参数
            filter_params = [
                {"key": "subject", "value": -1},
                {"key": "status", "value": -1}
            ]
            
            data = {
                'os': 'stuwin',
                'vc': '650',
                'vcname': '9.9.0',
                'deviceType': 'pc',
                'appId': 'winhaoke',
                'tabId': '181014',
                'lastOffsetFlag': '',
                'limit': '20',
                'filter': json.dumps(filter_params),
                'cancelKey': 'go-to-class-v4-list',
                'cuid': '4c-5f-70-95-cc-75'
            }

            # 🔧 使用POST请求，与源代码保持一致
            response = requests.post(
                'https://c4-jx-stable.zuoyebang.com/mcourse/winhaoke/course/list',
                data=data,
                cookies=self.cookies,
                headers=headers,
                verify=False,
                timeout=10
            )
            
            if response.status_code == 200:
                course_list_data = response.json()
                if course_list_data.get('errNo') == 0 and 'clCourseList' in course_list_data.get('data', {}):
                    courses = course_list_data['data']['clCourseList']
                    
                    # 🔧 按照源代码逻辑查找匹配的课程
                    for course in courses:
                        course_info_id = str(course['clCourseInfo']['courseId'])
                        if course_info_id == str(course_id):
                            title = course['clCardInfo']['title']
                            # 获取老师列表
                            teacher_names = [teacher['teacherName'] for teacher in course['clCardInfo']['clTeacherInfo']['clMainTeacherList']]
                            
                            if teacher_names:
                                # 使用第一个老师的名字（与源代码逻辑一致）
                                teacher_name = teacher_names[0]
                                combined_title = f"{title}-{teacher_name}"
                                return combined_title
                            else:
                                return title
                    
                    # 如果没找到匹配的课程ID，返回None
                    return None
            return None
            
        except Exception as e:
            print(f"⚠️ 获取课程老师信息失败: {str(e)}")
            return None

    def save_course_info(self, course_id, course_name):
        """保存课程ID和名称的映射关系到配置文件"""
        try:
            # 确保课程信息映射字段存在
            if "课程信息映射" not in self.config:
                self.config["课程信息映射"] = {"说明": "课程ID和课程名称的对应关系，程序运行时自动收集"}
            
            # 保存课程信息
            self.config["课程信息映射"][str(course_id)] = course_name
            
            # 写入配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            
        except Exception as e:
            print(f"⚠️ 保存课程信息失败: {str(e)}")

    def get_course_name_from_config(self, course_id):
        """从配置文件中获取课程名称"""
        try:
            if "课程信息映射" in self.config:
                return self.config["课程信息映射"].get(str(course_id), f"课程_{course_id}")
            return f"课程_{course_id}"
        except:
            return f"课程_{course_id}"

    def download_course(self, course_id):
        """下载单个课程"""
        print(f"\n🎯 开始处理课程ID: {course_id}")
        print(f"📱 使用账号: {self.current_account['账号名称']} ({self.current_account['用户名']})")
        
        try:
            headers = {
                'Host': 'c3-jx-stable.zuoyebang.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/640 a_irclass_vcname/9.8.0 appId/winhaoke'
            }
            
            # 获取课程目录（核心API调用）
            params = {
                'courseId': course_id,
                'appId': 'winhaoke',
            }
            
            response = requests.get(
                'https://c3-jx-stable.zuoyebang.com/frontcourse/teach/course/pccoursefull',
                params=params,
                cookies=self.cookies,
                headers=headers,
                verify=False,
                timeout=30  # 增加超时时间
            )
            
            #print(f"🔍 API响应状态: {response.status_code}")
            
            course_data = response.json()
            
            if course_data.get('errNo') != 0:
                print(f"❌ 获取课程信息失败: {course_data.get('errstr', '未知错误')}")
                print(f"🔍 详细错误信息: {course_data}")
                print(f"🔍 课程ID: {course_id}")
                print(f"🔍 账号: {self.current_account['用户名']}")
                return False
            
            # 提取课程信息
            lesson_list = course_data.get('data', {}).get('subItemInfo', {}).get('lessonList', [])
            if not lesson_list:
                print(f"⚠️ 课程 {course_id} 没有可下载的内容")
                return False
            
            # 🔧 使用新的方法获取包含老师信息的课程标题
            course_title_with_teacher = self.get_course_with_teacher_info(course_id)
            
            if course_title_with_teacher:
                print(f"📚 课程名称: {course_title_with_teacher}")
                safe_course_title = course_title_with_teacher.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
                # 保存课程信息到配置文件
                self.save_course_info(course_id, course_title_with_teacher)
            else:
                # 如果获取失败，使用基础课程标题
                basic_course_title = course_data.get('data', {}).get('courseInfo', {}).get('courseName', f"课程_{course_id}")
                print(f"📚 课程名称: {basic_course_title} (未获取到老师信息)")
                safe_course_title = basic_course_title.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
                # 保存基础课程信息到配置文件
                self.save_course_info(course_id, basic_course_title)
            
            print(f"📖 共找到 {len(lesson_list)} 个章节")
            
            # 创建课程文件夹，包含账号信息
            downloads_base = self.config["文件夹设置"]["真实文件目录"]
            account_folder = f"好课在线-{self.current_account['账号名称']}"
            course_folder = os.path.join(downloads_base, account_folder, safe_course_title)
            os.makedirs(course_folder, exist_ok=True)
            
            # 排序章节
            sorted_lesson_list = sorted(lesson_list, key=lambda x: x.get('index', 0))
            
            success_count = 0
            total_count = len(sorted_lesson_list)
            
            # 新增：连续失败计数和阈值
            fail_streak = 0
            fail_streak_limit = self.config.get("章节连续失败跳过阈值", 2)
            
            # 🚀 根据配置决定下载内容
            download_content_choice = self.config["下载设置"].get("下载内容选择", 1)
            
            if download_content_choice == 2:
                return self.download_course_materials_only(course_id, course_folder, safe_course_title)
            
            # 选项0或1：下载视频（选项0还会在最后下载独立课程资料）
            for lesson_index, lesson in enumerate(sorted_lesson_list, 1):
                if fail_streak >= fail_streak_limit:
                    print(f"⚠️ 连续失败已达{fail_streak_limit}次，跳过课程剩余章节！")
                    break
                
                lesson_info = lesson.get('lessonInfo', {})
                lesson_id = lesson_info.get('lessonId')
                lesson_name = lesson_info.get('lessonName')
                
                if not lesson_id or not lesson_name:
                    print(f"⚠️ 跳过无效章节 {lesson_index}")
                    continue
                
                # 获取直播间ID
                live_room_id = None
                integrate_room_info = lesson_info.get('integrateRoomInfo', {})
                if integrate_room_info:
                    room_info_list = integrate_room_info.get('roomInfo', [])
                    if room_info_list:
                        live_room_id = room_info_list[0].get('liveRoomId')
                
                if not live_room_id:
                    live_room_id = lesson.get('liveRoomList', [{}])[0].get('liveRoomId')
                
                # 📝 移除直播间ID检查 - 现在支持无直播间ID的章节
                # 我们的增强版get_video_address可以处理没有live_room_id的情况
                print(f"\n📝 处理章节 {lesson_index}/{total_count}: {lesson_name}")
                if live_room_id:
                    #print(f"🆔 直播间ID: {live_room_id}")
                    pass
                else:
                    #print("⚠️ 无直播间ID，将尝试多种API获取视频")
                    pass
                # 🚀 优化：先检查占位文件，避免不必要的网络请求
                lesson_folder_name = f"{str(lesson_index).zfill(2)}_{lesson_name}"
                lesson_folder_path = os.path.join(course_folder, lesson_folder_name)
                video_number = f"{str(lesson_index).zfill(2)}."
                video_filename = f"{video_number}{lesson_name}.mp4"
                potential_savefile = os.path.join(lesson_folder_path, self.add_suffix_to_filename(video_filename))
                
                # 检查占位文件是否存在
                if self.check_placeholder_exists(potential_savefile):
                    print(f"⏭️ 检测到占位文件，跳过章节: {lesson_name}")
                    success_count += 1  # 计入成功数量
                    continue
                
                # 检查真实文件是否存在
                if os.path.exists(potential_savefile):
                    print(f"⏭️ 真实文件已存在，跳过章节: {lesson_name}")
                    # 为已存在的文件创建占位文件
                    os.makedirs(lesson_folder_path, exist_ok=True)  # 确保文件夹存在
                    self.create_placeholder_file(potential_savefile)
                    success_count += 1  # 计入成功数量
                    continue
                
                # 🔧 优化：只有在需要下载时才发送网络请求获取视频地址
                video_address = self.get_video_address(lesson_id, course_id, live_room_id, lesson_name)
                if video_address:
                    # ✅ 只有在成功获取视频地址后才创建章节文件夹
                    print(f"✅ 成功获取视频地址，开始创建文件夹和下载")
                    os.makedirs(lesson_folder_path, exist_ok=True)
                    
                    temp_folder = os.path.join(lesson_folder_path, "temp")
                    os.makedirs(temp_folder, exist_ok=True)
                    
                    if self.download_video(video_address, potential_savefile, temp_folder, lesson_name):
                        success_count += 1
                        fail_streak = 0  # 成功则重置失败计数
                        
                        # 下载随堂资料
                        if self.config["下载设置"]["下载视频和资料"]:
                            print("📄 开始下载随堂资料...")
                            self.download_lesson_materials(lesson_id, lesson_folder_path, lesson_index, course_id)
                    else:
                        print(f"❌ 视频下载失败: {lesson_name}")
                        fail_streak += 1
                        # 如果视频下载失败，删除已创建的空文件夹
                        try:
                            if os.path.exists(lesson_folder_path) and not os.listdir(lesson_folder_path):
                                os.rmdir(lesson_folder_path)
                                print(f"🗑️ 已清理空文件夹: {lesson_folder_name}")
                        except Exception as e:
                            print(f"⚠️ 清理空文件夹失败: {str(e)}")
                else:
                    print(f"❌ 无法获取视频地址，跳过章节: {lesson_name}")
                    fail_streak += 1
                    # 不创建文件夹，直接跳过
            
            print(f"\n✅ 课程下载完成! 成功下载 {success_count}/{total_count} 个章节")
            
            # 🚀 如果选择全部内容(选项0)，额外下载独立的课程资料
            if download_content_choice == 0:
                print(f"\n📚 开始下载独立课程资料...")
                materials_success = self.download_course_materials_only(course_id, course_folder, safe_course_title, is_additional=True)
                if materials_success:
                    print(f"✅ 独立课程资料下载完成!")
                else:
                    print(f"⚠️ 独立课程资料下载失败或无资料")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理课程失败: {str(e)}")
            return False
    
    def download_lesson_materials(self, lesson_id, lesson_folder_path, lesson_index, course_id=None):
        """下载章节的随堂资料"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/640 a_irclass_vcname/9.8.0 appId/winhaoke'
            }
            
            params = {
                'os': 'stuwin',
                'vc': '640',
                'vcname': '9.8.0',
                'lessonId': lesson_id,
                'appId': 'winhaoke',
                'na__zyb_source__': 'winhaoke',
            }

            response = requests.get(
                'https://jx.zuoyebang.com/frontcourse/public/material/lessonmaterial',
                params=params,
                cookies=self.cookies,
                headers=headers,
                verify=False
            )
            
            data = response.json()
            
            # 🐛 调试：打印随堂资料完整响应
            # print(f"🔍 [调试] 随堂资料API响应 (lessonId: {lesson_id}):")
            # print(f"📝 URL: https://jx.zuoyebang.com/frontcourse/public/material/lessonmaterial")
            # print(f"📝 完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            # print("=" * 80)
            
            if data.get('errNo') == 0:
                materials = data.get('data', {}).get('materialList', [])
                if materials:
                    print(f"📄 找到 {len(materials)} 个课程资料")
                    success_count = 0
                    downloaded_files = set()
                    
                    for i, material in enumerate(materials, 1):
                        # 🚀 检查是否为子文件夹类型，需要递归处理
                        if material.get('folderType') == 1:  # 是文件夹
                            # 创建子文件夹路径
                            item_index = f"{str(lesson_index).zfill(2)}-{str(i).zfill(2)}"
                            folder_path = os.path.join(lesson_folder_path, material['title'])
                            os.makedirs(folder_path, exist_ok=True)
                            
                            print(f"📁 处理随堂资料子文件夹: {material['title']}")
                            
                            # 🚀 使用递归方法处理嵌套文件夹
                            if course_id:
                                folder_success, folder_total = self.download_folder_materials_recursive(
                                    material, folder_path, course_id, level=0, parent_index=item_index, parent_lesson_id=lesson_id
                                )
                            else:
                                print(f"⚠️ 缺少courseId，无法处理子文件夹: {material['title']}")
                                folder_success, folder_total = 0, 0
                            success_count += folder_success
                            # total_count 在这里没有统计，保持原有逻辑
                            
                        else:  # 直接文件
                            original_title = material['title']
                            file_title = f"{str(lesson_index).zfill(2)}.{original_title}"
                            
                            # 避免重复文件名
                            if file_title in downloaded_files:
                                base_name, ext = os.path.splitext(file_title)
                                counter = 1
                                while file_title in downloaded_files:
                                    file_title = f"{base_name}_{counter}{ext}"
                                    counter += 1
                            
                            material['title'] = file_title
                            if self.download_material(material, lesson_folder_path):
                                success_count += 1
                                downloaded_files.add(file_title)
                            material['title'] = original_title
                        
                    print(f"✅ 随堂资料下载完成，成功 {success_count}/{len(materials)} 个")
                else:
                    print("ℹ️ 该章节没有随堂资料")
            else:
                print(f"⚠️ 获取随堂资料失败：{data.get('errstr', '未知错误')}")
                
        except Exception as e:
            print(f"⚠️ 下载随堂资料异常：{str(e)}")
    
    def download_account_courses(self, account):
        """下载指定账号的所有课程"""
        print(f"\n🚀 开始处理账号: {account['账号名称']}")
        print("=" * 60)
        
        # 设置当前账号
        self.current_account = account
        
        # 登录账号
        if not self.auto_login(account):
            print(f"❌ 账号 {account['账号名称']} 登录失败，跳过")
            return False
        
        # 获取课程列表
        course_list = account.get("课程ID列表", [])
        if not course_list:
            print(f"⚠️ 账号 {account['账号名称']} 没有配置课程")
            return False
        
        total_courses = len(course_list)
        print(f"\n📋 账号 {account['账号名称']} 准备下载 {total_courses} 个课程")
        print("课程ID列表:", ", ".join(course_list))
        print("=" * 60)
        
        # 依次下载每个课程
        success_courses = 0
        for i, course_id in enumerate(course_list, 1):
            # 新增：前缀筛选逻辑
            if self.should_skip_course_by_prefix(course_id):
                course_name = self.get_course_name_from_config(course_id)
                print(f"⏭️ 课程 {course_id}（{course_name}）不符合前缀筛选，跳过")
                continue
            
            print(f"\n🎯 [{i}/{total_courses}] 开始下载课程: {course_id}")
            
            if self.download_course(course_id):
                success_courses += 1
                print(f"✅ 课程 {course_id} 下载完成")
            else:
                print(f"❌ 课程 {course_id} 下载失败")
        
        # 账号总结
        print(f"\n📊 账号 {account['账号名称']} 下载完成!")
        print(f"📈 成功下载: {success_courses}/{total_courses} 个课程")
        
        return success_courses == total_courses
    
    def run(self):
        """运行自动化下载器"""
        print("🚀 好课在线自动化下载器启动 - 多账号增强版")
        print("=" * 60)
        
        if not self.config:
            return False
        
        # 确保目录存在
        self.ensure_directories()
        
        # 获取执行模式
        execution_mode = self.config.get("执行模式", {}).get("模式", "选择账号")
        enabled_accounts = self.get_enabled_accounts()
        
        print(f"📋 发现 {len(enabled_accounts)} 个启用的账号")
        for account in enabled_accounts:
            course_list = account.get('课程ID列表', [])
            print(f"   - {account['账号名称']} ({account['用户名']}) - {len(course_list)} 个课程")
            # 显示具体的课程名称
            for course_id in course_list:
                course_name = self.get_course_name_from_config(course_id)
                print(f"     * {course_id}: {course_name}")
        
        # 根据执行模式处理
        if execution_mode == "依次执行":
            # 依次执行所有启用账号
            print(f"\n🔄 执行模式: 依次执行所有启用账号")
            success_accounts = 0
            total_accounts = len(enabled_accounts)
            
            for i, account in enumerate(enabled_accounts, 1):
                print(f"\n{'='*20} 账号 {i}/{total_accounts} {'='*20}")
                if self.download_account_courses(account):
                    success_accounts += 1
            
            # 总体总结
            print(f"\n🎉 所有账号处理完成!")
            print(f"📊 成功处理: {success_accounts}/{total_accounts} 个账号")
            return success_accounts == total_accounts
            
        elif execution_mode == "全部执行":
            print("⚠️ 暂不支持同时执行模式，将改为依次执行")
            return self.run_sequential_mode()
            
        else:  # 默认选择账号模式
            # 交互式选择账号
            selection = self.select_account()
            if selection == "all":
                # 用户选择执行所有账号
                success_accounts = 0
                total_accounts = len(enabled_accounts)
                
                for i, account in enumerate(enabled_accounts, 1):
                    print(f"\n{'='*20} 账号 {i}/{total_accounts} {'='*20}")
                    if self.download_account_courses(account):
                        success_accounts += 1
                
                print(f"\n🎉 所有账号处理完成!")
                print(f"📊 成功处理: {success_accounts}/{total_accounts} 个账号")
                return success_accounts == total_accounts
                
            elif isinstance(selection, list):
                # 用户选择了多个账号
                success_accounts = 0
                total_accounts = len(selection)
                
                for i, account in enumerate(selection, 1):
                    print(f"\n{'='*20} 账号 {i}/{total_accounts} {'='*20}")
                    if self.download_account_courses(account):
                        success_accounts += 1
                
                print(f"\n🎉 选定账号处理完成!")
                print(f"📊 成功处理: {success_accounts}/{total_accounts} 个账号")
                return success_accounts == total_accounts
                
            elif selection:
                # 用户选择了单个账号
                return self.download_account_courses(self.current_account)
            else:
                print("👋 已取消操作")
                return False

    def download_folder_materials_recursive(self, material, folder_path, course_id, level=0, parent_index="", parent_lesson_id=None):
        """递归下载文件夹中的材料，支持多层嵌套"""
        max_recursion_depth = 5  # 防止无限递归
        if level > max_recursion_depth:
            print(f"⚠️ 文件夹嵌套深度超过限制({max_recursion_depth})，跳过: {material['title']}")
            return 0, 0
        
        indent = "  " * level  # 根据层级添加缩进
        print(f"{indent}📁 处理{'子' if level > 0 else ''}文件夹: {material['title']}")
        
        success_count = 0
        total_count = 0
        
        try:
            # 🔧 获取文件夹内容（使用与原版代码相同的双API回退机制）
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/640 a_irclass_vcname/9.8.0 appId/winhaoke'
            }
            
            # 🔧 第一个API：尝试随堂资料API（与原版代码一致）
            params_file = {
                'os': 'stuwin',
                'vc': '640',
                'vcname': '9.8.0',
                'lessonId': material.get('lessonId', ''),
                'appId': 'winhaoke',
                'na__zyb_source__': 'winhaoke',
            }

            try:
                response_file = requests.get(
                    'https://jx.zuoyebang.com/frontcourse/public/material/lessonmaterial',
                    params=params_file,
                    cookies=self.cookies,
                    headers=headers,
                    verify=False,
                    timeout=10
                )
                
                folder_data = response_file.json()
                
                # 🐛 调试：打印文件夹资料完整响应 (第一个API)
                # print(f"{indent}🔍 [调试] 文件夹资料API响应 (第一个API, lessonId: {material.get('lessonId', '')}):")
                # print(f"{indent}📝 URL: https://jx.zuoyebang.com/frontcourse/public/material/lessonmaterial")
                # print(f"{indent}📝 完整响应: {json.dumps(folder_data, ensure_ascii=False, indent=2)}")
                # print(f"{indent}{'=' * 60}")

                # 🔧 如果第一个接口失败，尝试第二个接口（与原版代码一致）
                if folder_data.get('errNo') != 0 or 'materialList' not in folder_data.get('data', {}):
                    headers_c4 = {
                        'Host': 'jx2.zuoyebang.com',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/698 a_irclass_vcname/10.1.15 appId/winhaoke'
                    }
                    
                    params_file2 = {
                        'os': 'stuwin',
                        'vc': '698',
                        'vcname': '10.1.15',
                        'pid': material.get('pid', ''),
                        'lessonId': parent_lesson_id or '',  # 使用父级lessonId
                        'courseId': course_id,
                        'appId': 'winhaoke',
                        'na__zyb_source__': 'winhaoke'
                    }

                    response_file2 = requests.get(
                        'https://jx2.zuoyebang.com/mcourse/winhaoke/matearial/file',
                        params=params_file2,
                        cookies=self.cookies,
                        headers=headers_c4,
                        verify=False,
                        timeout=10
                    )
                    
                    folder_data = response_file2.json()
                    
                    # 🐛 调试：打印文件夹资料完整响应 (第二个API)
                    # print(f"{indent}🔍 [调试] 文件夹资料API响应 (第二个API, pid: {material.get('pid', '')}):")
                    # print(f"{indent}📝 URL: https://jx2.zuoyebang.com/mcourse/winhaoke/matearial/file")
                    # print(f"{indent}📝 请求参数: {params_file2}")
                    # print(f"{indent}📝 完整响应: {json.dumps(folder_data, ensure_ascii=False, indent=2)}")
                    # print(f"{indent}{'=' * 60}")

            except Exception as e:
                print(f"{indent}⚠️ 第一个API失败，尝试第二个API: {str(e)}")
                # 如果第一个API出现异常，直接尝试第二个API
                headers_c4 = {
                    'Host': 'jx2.zuoyebang.com',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/698 a_irclass_vcname/10.1.15 appId/winhaoke'
                }
                
                params_file2 = {
                    'os': 'stuwin',
                    'vc': '698',
                    'vcname': '10.1.15',
                    'pid': material.get('pid', ''),
                    'lessonId': parent_lesson_id or '',  # 使用父级lessonId
                    'courseId': course_id,
                    'appId': 'winhaoke',
                    'na__zyb_source__': 'winhaoke'
                }

                response_file2 = requests.get(
                    'https://jx2.zuoyebang.com/mcourse/winhaoke/matearial/file',
                    params=params_file2,
                    cookies=self.cookies,
                    headers=headers_c4,
                    verify=False,
                    timeout=10
                )
                
                folder_data = response_file2.json()
                
                # 🐛 调试：打印文件夹资料完整响应 (异常后的第二个API)
                # print(f"{indent}🔍 [调试] 文件夹资料API响应 (异常后第二个API, pid: {material.get('pid', '')}):")
                # print(f"{indent}📝 URL: https://jx2.zuoyebang.com/mcourse/winhaoke/matearial/file")
                # print(f"{indent}📝 请求参数: {params_file2}")
                # print(f"{indent}📝 完整响应: {json.dumps(folder_data, ensure_ascii=False, indent=2)}")
                # print(f"{indent}{'=' * 60}")
            
            if folder_data.get('errNo') == 0 and 'materialList' in folder_data.get('data', {}):
                sub_items = folder_data['data']['materialList']
                if sub_items:
                    print(f"{indent}📄 文件夹内找到 {len(sub_items)} 个项目")
                    
                    for j, sub_item in enumerate(sub_items, 1):
                        # 构建层级编号
                        if parent_index:
                            item_index = f"{parent_index}-{str(j).zfill(2)}"
                        else:
                            item_index = str(j).zfill(2)
                        
                        # 检查是否又是文件夹类型
                        if sub_item.get('folderType') == 1:
                            # 递归处理子文件夹
                            sub_folder_path = os.path.join(folder_path, sub_item['title'])
                            os.makedirs(sub_folder_path, exist_ok=True)
                            
                            sub_success, sub_total = self.download_folder_materials_recursive(
                                sub_item, sub_folder_path, course_id, level + 1, item_index, parent_lesson_id
                            )
                            success_count += sub_success
                            total_count += sub_total
                        else:
                            # 直接文件，下载
                            total_count += 1
                            original_title = sub_item['title']
                            sub_item['title'] = f"{item_index}.{original_title}"
                            
                            if self.download_material(sub_item, folder_path):
                                success_count += 1
                            
                            # 恢复原始标题
                            sub_item['title'] = original_title
                else:
                    print(f"{indent}📁 文件夹 {material['title']} 为空")
            else:
                print(f"{indent}⚠️ 获取文件夹内容失败: {folder_data.get('errstr', '未知错误')}")
                
        except Exception as e:
            print(f"{indent}⚠️ 处理文件夹失败: {str(e)}")
        
        return success_count, total_count

    def download_course_materials_only(self, course_id, course_folder=None, safe_course_title=None, is_additional=False):
        """仅下载课程资料模式"""
        try:
            if not is_additional:
                print(f"\n🎯 开始处理课程ID: {course_id} (仅下载课程资料)")
                print(f"📱 使用账号: {self.current_account['账号名称']} ({self.current_account['用户名']})")
            
            # 如果不是附加模式，需要创建课程文件夹
            if not course_folder:
                # 🔧 简化：直接获取基础课程信息
                headers = {
                    'Host': 'c3-jx-stable.zuoyebang.com',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/640 a_irclass_vcname/9.8.0 appId/winhaoke'
                }
                
                params = {
                    'courseId': course_id,
                    'appId': 'winhaoke',
                }
                
                response = requests.get(
                    'https://c3-jx-stable.zuoyebang.com/frontcourse/teach/course/pccoursefull',
                    params=params,
                    cookies=self.cookies,
                    headers=headers,
                    verify=False,
                    timeout=30
                )
                
                course_data = response.json()
                
                if course_data.get('errNo') != 0:
                    print(f"❌ 获取课程信息失败: {course_data.get('errstr', '未知错误')}")
                    return False
                
                # 使用基础课程标题
                course_title = course_data.get('data', {}).get('courseInfo', {}).get('courseName', f"课程_{course_id}")
                safe_course_title = course_title.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
                
                print(f"📚 课程名称: {safe_course_title}")
                
                # 创建课程文件夹
                downloads_base = self.config["文件夹设置"]["真实文件目录"]
                account_folder = f"好课在线-{self.current_account['账号名称']}"
                course_folder = os.path.join(downloads_base, account_folder, safe_course_title)
                os.makedirs(course_folder, exist_ok=True)
            
            # 下载课程级别的资料
            try:
                materials_folder = os.path.join(course_folder, "课程资料")
                
                # 🔧 修复：使用正确的课程资料API（参考原版源代码）
                headers = {
                    'Host': 'c4-jx-stable.zuoyebang.com',
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/650 a_irclass_vcname/9.9.0 appId/winhaoke'
                }
                
                # 🔧 修复：使用原版的课程资料API参数
                params = {
                    'os': 'stuwin',
                    'vc': '650',
                    'vcname': '9.9.0',
                    'courseId': course_id,
                    'appId': 'winhaoke',
                    'na__zyb_source__': 'winhaoke'
                }

                # 🔧 修复：使用原版的课程资料API端点
                response = requests.get(
                    'https://c4-jx-stable.zuoyebang.com/mcourse/winhaoke/matearial/course',
                    params=params,
                    cookies=self.cookies,
                    headers=headers,
                    verify=False
                )
                
                data = response.json()
                
                # 🐛 调试：打印课程资料完整响应
                # print(f"🔍 [调试] 课程资料API响应 (courseId: {course_id}):")
                # print(f"📝 URL: https://c4-jx-stable.zuoyebang.com/mcourse/winhaoke/matearial/course")
                # print(f"📝 完整响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                # print("=" * 80)
                
                if data.get('errNo') == 0:
                    # 🔧 修复：使用原版的数据结构字段名
                    materials = data.get('data', {}).get('courseMaterialList', [])
                    if materials:
                        print(f"📄 找到 {len(materials)} 个课程资料")
                        os.makedirs(materials_folder, exist_ok=True)
                        
                        success_count = 0
                        total_count = 0
                        
                        for i, material in enumerate(materials, 1):
                            # 🔧 优化：使用递归方法处理文件夹类型
                            if material.get('folderType') == 1:  # 是文件夹
                                folder_path = os.path.join(materials_folder, material['title'])
                                os.makedirs(folder_path, exist_ok=True)
                                
                                # 🚀 使用递归方法处理嵌套文件夹
                                folder_success, folder_total = self.download_folder_materials_recursive(
                                    material, folder_path, course_id, level=0, parent_index=str(i).zfill(2), parent_lesson_id=material.get('lessonId')
                                )
                                success_count += folder_success
                                total_count += folder_total
                                    
                            else:  # 直接文件
                                total_count += 1
                                # 为课程资料添加编号
                                original_title = material['title']
                                material['title'] = f"{str(i).zfill(2)}.{original_title}"
                                
                                if self.download_material(material, materials_folder):
                                    success_count += 1
                                
                                # 恢复原始标题
                                material['title'] = original_title
                        
                        print(f"✅ 课程资料下载完成，成功 {success_count}/{total_count} 个")
                        return success_count > 0
                    else:
                        if not is_additional:
                            print("ℹ️ 该课程没有课程资料")
                        return False
                else:
                    if not is_additional:
                        print(f"⚠️ 获取课程资料失败：{data.get('errstr', '未知错误')}")
                    return False
                    
            except Exception as e:
                if not is_additional:
                    print(f"⚠️ 下载课程资料异常：{str(e)}")
                return False
                
        except Exception as e:
            print(f"❌ 课程资料下载失败: {str(e)}")
            return False

    def download_file_multithread(self, url, file_path, cookies=None, max_threads=8):
        """多线程分块下载文件"""
        try:
            # 创建session
            session = requests.Session()
            if cookies:
                session.cookies.update(cookies)

            # 判断是否为资料下载（通过调用方传递的headers或file_path后缀判断）
            headers = self.get_material_download_headers(url)
            session.headers.update(headers)

            # 1. 获取文件信息
            print(f"🔍 正在获取文件信息...")
            head_response = session.head(url, verify=False, timeout=10)
            head_response.raise_for_status()
            
            # 检查是否支持Range请求
            accept_ranges = head_response.headers.get('Accept-Ranges', '').lower()
            content_length = head_response.headers.get('Content-Length')
            
            if not content_length:
                print(f"⚠️ 无法获取文件大小，回退到单线程下载")
                return self.download_file_single_thread(url, file_path, session)
                
            file_size = int(content_length)
            file_size_mb = file_size / (1024 * 1024)
            
            # 2. 智能判断是否使用多线程
            if accept_ranges != 'bytes':
                print(f"⚠️ 服务器不支持Range请求，回退到单线程下载")
                return self.download_file_single_thread(url, file_path, session)
                
            # 根据文件大小决定线程数
            if file_size < 5 * 1024 * 1024:  # 小于5MB
                print(f"📁 文件较小 ({file_size_mb:.1f}MB)，使用单线程下载")
                return self.download_file_single_thread(url, file_path, session)
            elif file_size < 20 * 1024 * 1024:  # 5-20MB
                actual_threads = min(4, max_threads)
            else:  # 大于20MB
                actual_threads = max_threads
                
            print(f"🚀 文件大小: {file_size_mb:.1f}MB，使用 {actual_threads} 线程分块下载")
            
            # 3. 创建临时目录
            temp_dir = os.path.join(os.path.dirname(file_path), f".temp_{uuid.uuid4().hex[:8]}")
            os.makedirs(temp_dir, exist_ok=True)
            
            try:
                # 4. 计算分块
                chunk_size = file_size // actual_threads
                download_tasks = []
                
                for i in range(actual_threads):
                    start_byte = i * chunk_size
                    if i == actual_threads - 1:  # 最后一块包含剩余所有字节
                        end_byte = file_size - 1
                    else:
                        end_byte = start_byte + chunk_size - 1
                    
                    download_tasks.append((start_byte, end_byte, i))
                
                print(f"📊 分块详情: {len(download_tasks)} 个分块，每块约 {chunk_size/(1024*1024):.1f}MB")
                
                # 5. 多线程下载
                chunk_files = []
                total_downloaded = 0
                failed_chunks = []
                
                filename_base = os.path.basename(file_path)
                
                with ThreadPoolExecutor(max_workers=actual_threads) as executor:
                    # 提交所有下载任务
                    future_to_chunk = {}
                    for start_byte, end_byte, chunk_index in download_tasks:
                        chunk_session = requests.Session()
                        chunk_session.headers.update(headers)
                        if cookies:
                            chunk_session.cookies.update(cookies)
                        
                        future = executor.submit(
                            self.download_chunk, 
                            chunk_session, url, start_byte, end_byte, 
                            chunk_index, temp_dir, filename_base
                        )
                        future_to_chunk[future] = (chunk_index, end_byte - start_byte + 1)
                    
                    # 收集结果
                    with tqdm(total=file_size, unit='B', unit_scale=True, 
                             desc=f"下载 {os.path.basename(file_path)}") as pbar:
                        for future in as_completed(future_to_chunk):
                            chunk_index, expected_size = future_to_chunk[future]
                            chunk_file, downloaded_size = future.result()
                            
                            if chunk_file:
                                chunk_files.append((chunk_index, chunk_file))
                                total_downloaded += downloaded_size
                                pbar.update(downloaded_size)
                            else:
                                failed_chunks.append(chunk_index)
                
                # 6. 检查下载完整性
                if failed_chunks:
                    print(f"❌ 有 {len(failed_chunks)} 个分块下载失败，回退到单线程下载")
                    shutil.rmtree(temp_dir)
                    return self.download_file_single_thread(url, file_path, session)
                
                if total_downloaded != file_size:
                    print(f"❌ 下载大小不匹配: {total_downloaded}/{file_size}，回退到单线程下载")
                    shutil.rmtree(temp_dir)
                    return self.download_file_single_thread(url, file_path, session)
                
                # 7. 合并文件
                print(f"🔄 正在合并分块文件...")
                chunk_files.sort(key=lambda x: x[0])  # 按分块索引排序
                
                with open(file_path, 'wb') as outfile:
                    for chunk_index, chunk_file in chunk_files:
                        with open(chunk_file, 'rb') as infile:
                            shutil.copyfileobj(infile, outfile)
                
                # 8. 清理临时文件
                shutil.rmtree(temp_dir)
                
                # 9. 验证最终文件
                if os.path.exists(file_path) and os.path.getsize(file_path) == file_size:
                    print(f"✅ 多线程下载完成: {os.path.basename(file_path)} ({file_size_mb:.1f}MB)")
                    return True
                else:
                    print(f"❌ 最终文件验证失败")
                    return False
                    
            except Exception as e:
                print(f"❌ 多线程下载过程出错: {str(e)}")
                # 清理临时目录
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                # 回退到单线程下载
                return self.download_file_single_thread(url, file_path, session)
                
        except Exception as e:
            print(f"❌ 多线程下载初始化失败: {str(e)}")
            return False

    def download_file_single_thread(self, url, file_path, session=None):
        """单线程下载文件（带进度条显示）"""
        if not session:
            session = requests.Session()
            headers = self.get_material_download_headers(url)
            session.headers.update(headers)
            if self.cookies:
                session.cookies.update(self.cookies)
        else:
            # 若外部传入session，也补全headers
            session.headers.update(self.get_material_download_headers(url))

        try:
            with session.get(url, stream=True, verify=False, timeout=30) as response:
                response.raise_for_status()
                chunk_size = 1024 * 256  # 256KB
                downloaded_size = 0
                start_time = time.time()
                
                with open(file_path, 'wb') as f:
                    with tqdm(unit='B', unit_scale=True, desc=f"下载 {os.path.basename(file_path)}") as pbar:
                        for chunk in response.iter_content(chunk_size=chunk_size):
                            if chunk:
                                f.write(chunk)
                                downloaded_size += len(chunk)
                                pbar.update(len(chunk))
                                
                                # 计算下载速度
                                elapsed_time = time.time() - start_time
                                if elapsed_time > 0:
                                    speed = downloaded_size / elapsed_time
                                    pbar.set_postfix({'速度': f'{speed/1024/1024:.1f}MB/s'})
                
                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    return True
                else:
                    return False
        except Exception as e:
            print(f"❌ 单线程下载失败: {str(e)}")
            return False

    def should_skip_course_by_prefix(self, course_id):
        """判断课程名是否符合前缀筛选要求，不符合则返回True（跳过）"""
        # 获取前缀列表
        prefix_list = self.config.get("课程前缀筛选", None)
        if not prefix_list or not isinstance(prefix_list, list) or len(prefix_list) == 0:
            return False  # 未配置则全部处理
        # 获取课程名
        course_name = self.get_course_name_from_config(course_id)
        # 若未获取到课程名，尝试API补全
        if course_name.startswith("课程_"):
            # 尝试API补全
            api_name = self.get_course_with_teacher_info(course_id)
            if api_name:
                course_name = api_name
                self.save_course_info(course_id, api_name)
        # 判断前缀
        for prefix in prefix_list:
            if course_name.startswith(prefix):
                return False  # 匹配前缀，不跳过
        return True  # 不匹配，跳过

    def get_material_download_headers(self, file_url):
        """
        构造资料下载请求头，模拟官方页面行为，提升下载成功率，避免403。
        仅用于资料文件下载，不影响视频下载和API请求。
        """
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.199 Safari/537.36 a_irclass_vc/698 a_irclass_vcname/10.1.15 appId/winhaoke',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate',
            'na__zyb_source__': 'winhaoke',
        }
        # Referer可设为课程资料API页面或主域
        headers['Referer'] = 'https://jx2.zuoyebang.com/'
        return headers

    def download_chunk(self, session, url, start_byte, end_byte, chunk_index, temp_dir, filename):
        """下载单个文件分块"""
        chunk_file = os.path.join(temp_dir, f"{filename}.part{chunk_index}")
        
        # 如果分块文件已存在且大小正确，跳过下载
        expected_size = end_byte - start_byte + 1
        if os.path.exists(chunk_file) and os.path.getsize(chunk_file) == expected_size:
            return chunk_file, expected_size
            
        try:
            headers = self.get_material_download_headers(url)
            headers['Range'] = f'bytes={start_byte}-{end_byte}'
            
            response = session.get(url, headers=headers, stream=True, verify=False, timeout=30)
            response.raise_for_status()
            
            downloaded_size = 0
            with open(chunk_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=32768):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
            
            # 验证分块大小
            if downloaded_size != expected_size:
                print(f"⚠️ 分块 {chunk_index} 下载大小不匹配: {downloaded_size}/{expected_size}")
                if os.path.exists(chunk_file):
                    os.remove(chunk_file)
                return None, 0
                
            return chunk_file, downloaded_size
            
        except Exception as e:
            print(f"❌ 分块 {chunk_index} 下载失败: {str(e)}")
            if os.path.exists(chunk_file):
                os.remove(chunk_file)
            return None, 0


def main():
    """主入口函数"""
    downloader = AutoHaokeDownloader()
    
    try:
        success = downloader.run()
        if success:
            print("\n🎉 所有任务下载成功!")
        else:
            print("\n⚠️ 部分任务下载失败，请查看日志了解详情")
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断下载")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        logging.exception("程序异常")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main() 