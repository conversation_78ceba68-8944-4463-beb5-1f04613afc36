<?php
/**
 * 通用安全框架 - 授权管理类
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 防止直接访问
if (!defined('SECURITY_FRAMEWORK')) {
    die('Direct access denied');
}

/**
 * 授权管理类
 * 处理授权验证、设备管理等核心功能
 */
class LicenseManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 验证授权许可
     * 
     * @param string $licenseKey 授权码
     * @param string $deviceId 设备ID
     * @param string $appId 应用ID
     * @param array $requestData 请求数据
     * @return array 验证结果
     */
    public function verifyLicense($licenseKey, $deviceId, $appId, $requestData = []) {
        $startTime = microtime(true);
        
        try {
            // 查询授权信息
            $license = $this->db->fetchOne(
                "SELECT * FROM licenses WHERE license_key = ? AND app_id = ?",
                [$licenseKey, $appId]
            );
            
            if (!$license) {
                return $this->createErrorResult('LICENSE_NOT_FOUND', '授权码不存在');
            }
            
            // 检查授权状态
            $statusCheck = $this->checkLicenseStatus($license);
            if (!$statusCheck['success']) {
                return $statusCheck;
            }
            
            // 验证设备绑定
            $deviceResult = $this->verifyDevice($license, $deviceId, $requestData);
            if (!$deviceResult['success']) {
                return $deviceResult;
            }
            
            // 更新最后验证时间
            $this->updateLastVerification($license['id'], $deviceId);
            
            // 记录成功的验证
            $responseTime = round((microtime(true) - $startTime) * 1000);
            $this->logVerification($licenseKey, $deviceId, $appId, 'success', null, null, $responseTime, $requestData);
            
            return [
                'success' => true,
                'message' => '验证成功',
                'data' => [
                    'license_type' => $license['license_type'],
                    'expire_date' => $license['expire_date'],
                    'user_name' => $license['user_name'],
                    'max_devices' => $license['max_devices'],
                    'used_devices' => $license['used_devices']
                ]
            ];
            
        } catch (Exception $e) {
            writeLog('ERROR', 'License verification error', [
                'license_key' => $licenseKey,
                'device_id' => $deviceId,
                'app_id' => $appId,
                'error' => $e->getMessage()
            ]);
            
            return $this->createErrorResult('VERIFY_ERROR', '验证过程发生错误');
        }
    }
    
    /**
     * 检查授权状态
     * 
     * @param array $license 授权信息
     * @return array 检查结果
     */
    private function checkLicenseStatus($license) {
        // 检查是否被禁用
        if ($license['status'] === 'banned') {
            return $this->createErrorResult('LICENSE_BANNED', '授权已被禁用');
        }
        
        if ($license['status'] === 'expired') {
            return $this->createErrorResult('LICENSE_EXPIRED', '授权已过期');
        }
        
        if ($license['status'] === 'pending') {
            return $this->createErrorResult('LICENSE_PENDING', '授权待激活');
        }
        
        // 检查过期时间
        if ($license['expire_date'] && strtotime($license['expire_date']) < time()) {
            // 更新状态为过期
            $this->db->update('licenses', 
                ['status' => 'expired'], 
                'id = ?', 
                [$license['id']]
            );
            
            return $this->createErrorResult('LICENSE_EXPIRED', '授权已过期');
        }
        
        return ['success' => true];
    }
    
    /**
     * 验证设备绑定
     * 
     * @param array $license 授权信息
     * @param string $deviceId 设备ID
     * @param array $requestData 请求数据
     * @return array 验证结果
     */
    private function verifyDevice($license, $deviceId, $requestData) {
        // 查询设备信息
        $device = $this->db->fetchOne(
            "SELECT * FROM devices WHERE device_id = ? AND license_id = ?",
            [$deviceId, $license['id']]
        );
        
        if ($device) {
            // 设备已存在，检查状态
            if ($device['status'] === 'banned') {
                return $this->createErrorResult('DEVICE_BANNED', '设备已被禁用');
            }
            
            if ($device['status'] === 'suspicious') {
                // 可疑设备，增加风险评分
                $this->updateDeviceRiskScore($device['id'], 10);
                writeLog('WARNING', 'Suspicious device access', [
                    'device_id' => $deviceId,
                    'license_id' => $license['id'],
                    'risk_score' => $device['risk_score'] + 10
                ]);
            }
            
            // 更新设备信息
            $this->updateDeviceInfo($device['id'], $requestData);
            
            return ['success' => true];
        }
        
        // 新设备，检查是否超出限制
        $deviceCount = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM devices WHERE license_id = ? AND status = 'active'",
            [$license['id']]
        );
        
        if ($deviceCount >= $license['max_devices']) {
            return $this->createErrorResult('DEVICE_LIMIT_EXCEEDED', 
                sprintf('设备数量已达上限(%d/%d)', $deviceCount, $license['max_devices']));
        }
        
        // 注册新设备
        $this->registerDevice($license['id'], $deviceId, $requestData);
        
        return ['success' => true];
    }
    
    /**
     * 注册新设备
     * 
     * @param int $licenseId 授权ID
     * @param string $deviceId 设备ID
     * @param array $requestData 请求数据
     */
    private function registerDevice($licenseId, $deviceId, $requestData) {
        $clientIP = getClientIP();
        $now = date('Y-m-d H:i:s');
        
        $deviceData = [
            'device_id' => $deviceId,
            'license_id' => $licenseId,
            'device_name' => $requestData['device_name'] ?? 'Unknown Device',
            'os_info' => $requestData['os_info'] ?? '',
            'hardware_info' => json_encode($requestData['hardware_info'] ?? [], JSON_UNESCAPED_UNICODE),
            'first_ip' => $clientIP,
            'last_ip' => $clientIP,
            'first_login' => $now,
            'last_login' => $now,
            'login_count' => 1,
            'status' => 'active',
            'risk_score' => 0
        ];
        
        $deviceDbId = $this->db->insert('devices', $deviceData);
        
        // 更新授权表的已使用设备数
        $this->db->query(
            "UPDATE licenses SET used_devices = used_devices + 1 WHERE id = ?",
            [$licenseId]
        );
        
        writeLog('INFO', 'New device registered', [
            'device_id' => $deviceId,
            'license_id' => $licenseId,
            'device_db_id' => $deviceDbId,
            'client_ip' => $clientIP
        ]);
    }
    
    /**
     * 更新设备信息
     * 
     * @param int $deviceDbId 设备数据库ID
     * @param array $requestData 请求数据
     */
    private function updateDeviceInfo($deviceDbId, $requestData) {
        $updateData = [
            'last_ip' => getClientIP(),
            'last_login' => date('Y-m-d H:i:s'),
            'login_count' => new PDOExpression('login_count + 1')
        ];
        
        // 更新操作系统信息
        if (!empty($requestData['os_info'])) {
            $updateData['os_info'] = $requestData['os_info'];
        }
        
        // 更新硬件信息
        if (!empty($requestData['hardware_info'])) {
            $updateData['hardware_info'] = json_encode($requestData['hardware_info'], JSON_UNESCAPED_UNICODE);
        }
        
        $this->db->update('devices', $updateData, 'id = ?', [$deviceDbId]);
    }
    
    /**
     * 更新设备风险评分
     * 
     * @param int $deviceDbId 设备数据库ID
     * @param int $scoreIncrease 评分增加值
     */
    private function updateDeviceRiskScore($deviceDbId, $scoreIncrease) {
        $this->db->update('devices', [
            'risk_score' => new PDOExpression("risk_score + {$scoreIncrease}")
        ], 'id = ?', [$deviceDbId]);
        
        // 检查是否需要标记为可疑
        $device = $this->db->fetchOne("SELECT risk_score FROM devices WHERE id = ?", [$deviceDbId]);
        if ($device && $device['risk_score'] > 50) {
            $this->db->update('devices', [
                'status' => 'suspicious'
            ], 'id = ?', [$deviceDbId]);
        }
    }
    
    /**
     * 更新最后验证时间
     * 
     * @param int $licenseId 授权ID
     * @param string $deviceId 设备ID
     */
    private function updateLastVerification($licenseId, $deviceId) {
        $this->db->update('licenses', [
            'last_verify_time' => date('Y-m-d H:i:s'),
            'last_verify_ip' => getClientIP(),
            'total_verifications' => new PDOExpression('total_verifications + 1')
        ], 'id = ?', [$licenseId]);
    }
    
    /**
     * 记录验证日志
     */
    private function logVerification($licenseKey, $deviceId, $appId, $result, $errorCode = null, $errorMessage = null, $responseTime = null, $requestData = []) {
        $logData = [
            'license_key' => $licenseKey,
            'device_id' => $deviceId,
            'app_id' => $appId,
            'client_version' => $requestData['client_version'] ?? null,
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'request_data' => json_encode($requestData, JSON_UNESCAPED_UNICODE),
            'verify_result' => $result,
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'response_time' => $responseTime
        ];
        
        $this->db->insert('verify_logs', $logData);
    }
    
    /**
     * 创建错误结果
     * 
     * @param string $errorCode 错误代码
     * @param string $message 错误消息
     * @return array 错误结果
     */
    private function createErrorResult($errorCode, $message) {
        return [
            'success' => false,
            'message' => $message,
            'error_code' => $errorCode
        ];
    }
    
    /**
     * 生成新的授权码
     * 
     * @param string $appId 应用ID
     * @param string $licenseType 授权类型
     * @param int $expireDays 有效期天数
     * @param int $maxDevices 最大设备数
     * @param array $userData 用户数据
     * @return array 生成结果
     */
    public function generateLicense($appId, $licenseType, $expireDays = null, $maxDevices = 1, $userData = []) {
        // 生成唯一授权码
        do {
            $licenseKey = Security::generateHexString(32);
            $exists = $this->db->fetchOne("SELECT id FROM licenses WHERE license_key = ?", [$licenseKey]);
        } while ($exists);
        
        // 计算过期时间
        $expireDate = null;
        if ($expireDays && $licenseType !== 'permanent') {
            $expireDate = date('Y-m-d H:i:s', time() + ($expireDays * 24 * 60 * 60));
        }
        
        $licenseData = [
            'license_key' => $licenseKey,
            'app_id' => $appId,
            'user_name' => $userData['user_name'] ?? null,
            'user_contact' => $userData['user_contact'] ?? null,
            'license_type' => $licenseType,
            'expire_date' => $expireDate,
            'max_devices' => $maxDevices,
            'status' => 'active',
            'notes' => $userData['notes'] ?? null
        ];
        
        $licenseId = $this->db->insert('licenses', $licenseData);
        
        writeLog('INFO', 'New license generated', [
            'license_id' => $licenseId,
            'license_key' => $licenseKey,
            'app_id' => $appId,
            'license_type' => $licenseType,
            'expire_date' => $expireDate
        ]);
        
        return [
            'license_id' => $licenseId,
            'license_key' => $licenseKey,
            'expire_date' => $expireDate,
            'license_type' => $licenseType,
            'max_devices' => $maxDevices
        ];
    }
}

?>
