# 通用安全框架开发文档

## 项目概述

### 目标
为多个自动化下载项目提供统一的在线安全验证服务，防止软件被破解和滥用。

### 核心特性
- 🔒 **纯在线验证** - 无本地缓存，实时验证授权状态
- 🛡️ **多层安全防护** - 设备绑定、请求签名、反调试保护
- 🔄 **自动更新系统** - 版本控制、强制更新、增量下载
- 📊 **统一管理后台** - 用户管理、授权控制、数据统计
- 🚀 **轻量级集成** - 简单SDK，最小化对现有项目的影响

### 技术架构
```
客户端应用 → Python SDK → HTTPS API → PHP后端 → MySQL数据库
```

## 环境配置

### 服务器信息
- **域名**: http://haoke.kechengmao.top/
- **数据库**: haoke_kechengmao
- **用户**: haoke_kechengmao  
- **密码**: 9Fyc8e9HRXbDbzmX

### 开发环境要求
- **服务端**: PHP 7.4+, MySQL 5.7+, Nginx/Apache
- **客户端**: Python 3.7+
- **工具**: Git, Composer, pip

## 数据库设计

### 1. 应用管理表 (apps)
```sql
CREATE TABLE `apps` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `app_name` varchar(100) NOT NULL COMMENT '应用名称',
    `app_secret` varchar(64) NOT NULL COMMENT '应用密钥',
    `version` varchar(20) DEFAULT '1.0.0' COMMENT '当前版本',
    `download_url` text COMMENT '下载地址',
    `update_message` text COMMENT '更新说明',
    `force_update` tinyint(1) DEFAULT 0 COMMENT '强制更新',
    `min_version` varchar(20) DEFAULT NULL COMMENT '最低兼容版本',
    `status` tinyint(1) DEFAULT 1 COMMENT '应用状态',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用管理表';
```

### 2. 授权许可表 (licenses)
```sql
CREATE TABLE `licenses` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `license_key` varchar(64) NOT NULL COMMENT '授权码',
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
    `user_contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
    `license_type` enum('trial','daily','weekly','monthly','yearly','permanent') DEFAULT 'trial' COMMENT '授权类型',
    `expire_date` datetime DEFAULT NULL COMMENT '过期时间',
    `max_devices` int(11) DEFAULT 1 COMMENT '最大设备数',
    `used_devices` int(11) DEFAULT 0 COMMENT '已使用设备数',
    `total_verifications` int(11) DEFAULT 0 COMMENT '总验证次数',
    `last_verify_time` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
    `last_verify_ip` varchar(45) DEFAULT NULL COMMENT '最后验证IP',
    `status` enum('active','expired','banned','pending') DEFAULT 'pending' COMMENT '状态',
    `notes` text COMMENT '备注信息',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `license_key` (`license_key`),
    KEY `app_id` (`app_id`),
    KEY `status` (`status`),
    KEY `expire_date` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权许可表';
```

### 3. 设备管理表 (devices)
```sql
CREATE TABLE `devices` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` varchar(128) NOT NULL COMMENT '设备唯一ID',
    `license_id` int(11) NOT NULL COMMENT '授权ID',
    `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
    `os_info` varchar(200) DEFAULT NULL COMMENT '操作系统信息',
    `hardware_info` text COMMENT '硬件信息JSON',
    `first_ip` varchar(45) DEFAULT NULL COMMENT '首次注册IP',
    `last_ip` varchar(45) DEFAULT NULL COMMENT '最后使用IP',
    `first_login` timestamp NULL DEFAULT NULL COMMENT '首次登录时间',
    `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
    `status` enum('active','banned','suspicious') DEFAULT 'active' COMMENT '设备状态',
    `risk_score` int(11) DEFAULT 0 COMMENT '风险评分',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `device_license` (`device_id`, `license_id`),
    KEY `license_id` (`license_id`),
    KEY `device_id` (`device_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备管理表';
```

### 4. 验证日志表 (verify_logs)
```sql
CREATE TABLE `verify_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `license_key` varchar(64) NOT NULL COMMENT '授权码',
    `device_id` varchar(128) NOT NULL COMMENT '设备ID',
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `client_version` varchar(20) DEFAULT NULL COMMENT '客户端版本',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `request_data` text COMMENT '请求数据JSON',
    `verify_result` enum('success','failed','banned','expired','invalid') NOT NULL COMMENT '验证结果',
    `error_code` varchar(20) DEFAULT NULL COMMENT '错误代码',
    `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
    `response_time` int(11) DEFAULT NULL COMMENT '响应时间(ms)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `license_key` (`license_key`),
    KEY `device_id` (`device_id`),
    KEY `app_id` (`app_id`),
    KEY `verify_result` (`verify_result`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证日志表';
```

### 5. 系统配置表 (system_config)
```sql
CREATE TABLE `system_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `config_type` enum('string','int','bool','json') DEFAULT 'string' COMMENT '配置类型',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开配置',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 6. 管理员表 (admins)
```sql
CREATE TABLE `admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码哈希',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `role` enum('super','admin','operator') DEFAULT 'operator' COMMENT '角色',
    `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

## 服务端开发

### 目录结构
```
haoke.kechengmao.top/
├── api/                        # API接口目录
│   ├── auth/                   # 认证相关
│   │   ├── verify.php         # 在线验证
│   │   ├── activate.php       # 激活授权
│   │   └── heartbeat.php      # 心跳检测
│   ├── device/                # 设备管理
│   │   ├── register.php       # 设备注册
│   │   ├── info.php          # 设备信息
│   │   └── unbind.php        # 解绑设备
│   ├── update/                # 更新相关
│   │   ├── check.php         # 检查更新
│   │   ├── download.php      # 下载更新
│   │   └── changelog.php     # 更新日志
│   └── admin/                 # 管理接口
│       ├── login.php         # 管理员登录
│       ├── dashboard.php     # 仪表板数据
│       ├── licenses.php      # 授权管理
│       ├── devices.php       # 设备管理
│       └── logs.php          # 日志查询
├── includes/                  # 核心类库
│   ├── config.php            # 配置文件
│   ├── database.php          # 数据库类
│   ├── security.php          # 安全类库
│   ├── license.php           # 授权管理类
│   ├── device.php            # 设备管理类
│   ├── logger.php            # 日志记录类
│   └── utils.php             # 工具函数
├── admin/                     # 管理后台
│   ├── index.php             # 后台首页
│   ├── login.php             # 登录页面
│   ├── dashboard.php         # 仪表板
│   ├── licenses/             # 授权管理页面
│   ├── devices/              # 设备管理页面
│   ├── logs/                 # 日志查看页面
│   └── settings/             # 系统设置页面
├── assets/                    # 静态资源
│   ├── css/                  # 样式文件
│   ├── js/                   # JavaScript文件
│   └── images/               # 图片资源
├── uploads/                   # 上传文件目录
├── logs/                      # 日志文件目录
└── .htaccess                  # Apache重写规则
```

## 核心安全机制

### 1. 在线验证流程
```
客户端启动 → 生成设备指纹 → 发送验证请求 → 服务端验证 → 返回结果
     ↓              ↓              ↓              ↓           ↓
设备信息收集 → 请求签名加密 → API接口调用 → 数据库查询 → 授权状态
```

### 2. 设备指纹算法
- MAC地址 + CPU信息 + 系统版本 + 硬件UUID
- SHA256哈希生成唯一标识
- 防止虚拟机和模拟器绕过

### 3. 请求签名机制
- HMAC-SHA256签名算法
- 时间戳防重放攻击
- 参数排序确保一致性

### 4. 反调试保护
- 调试器检测
- 时间异常检测
- 进程监控
- 内存保护

## 开发计划

### 第一阶段：基础框架 (1-2周)
1. 数据库表结构创建
2. PHP核心类库开发
3. 基础API接口实现
4. Python SDK开发

### 第二阶段：安全增强 (1周)
1. 反调试模块集成
2. 请求签名验证
3. 设备指纹优化
4. 安全测试

### 第三阶段：管理后台 (1周)
1. 管理界面开发
2. 数据统计功能
3. 用户管理系统
4. 日志查看功能

### 第四阶段：集成测试 (1周)
1. 好课在线项目集成
2. 功能测试验证
3. 性能优化
4. 文档完善

## 安全考虑

### 服务端安全
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证
- 输入数据验证
- 错误信息隐藏

### 通信安全
- HTTPS强制加密
- API请求签名
- 时间戳验证
- 频率限制
- IP白名单

### 客户端安全
- 代码混淆
- 反调试保护
- 设备绑定
- 在线验证
- 自动更新

## 部署指南

### 服务器配置
1. 安装PHP 7.4+和MySQL 5.7+
2. 配置SSL证书
3. 设置防火墙规则
4. 配置日志轮转

### 数据库初始化
1. 创建数据库和用户
2. 执行表结构SQL
3. 插入初始配置数据
4. 创建管理员账户

### 应用部署
1. 上传代码文件
2. 配置数据库连接
3. 设置文件权限
4. 测试API接口

## PHP核心类库实现

### 1. 配置文件 (includes/config.php)
```php
<?php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'haoke_kechengmao');
define('DB_USER', 'haoke_kechengmao');
define('DB_PASS', '9Fyc8e9HRXbDbzmX');
define('DB_CHARSET', 'utf8mb4');

// 安全配置
define('API_SECRET_KEY', 'your-super-secret-api-key-2024');
define('ENCRYPTION_KEY', 'your-32-char-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-for-admin-auth');

// 系统配置
define('SYSTEM_TIMEZONE', 'Asia/Shanghai');
define('LOG_LEVEL', 'INFO');
define('MAX_DEVICES_PER_LICENSE', 3);
define('VERIFY_TIMEOUT', 30); // 验证超时时间(秒)

// API配置
define('API_VERSION', 'v1');
define('RATE_LIMIT_REQUESTS', 100); // 每分钟最大请求数
define('RATE_LIMIT_WINDOW', 60); // 限制窗口(秒)

// 安全头设置
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// 错误报告设置
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 时区设置
date_default_timezone_set(SYSTEM_TIMEZONE);
?>
```

### 2. 数据库类 (includes/database.php)
```php
<?php
class Database {
    private $connection;
    private static $instance = null;

    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];

            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("数据库连接失败");
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            throw new Exception("数据库查询失败");
        }
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        $sql = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";

        $stmt = $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $field) {
            $setClause[] = "{$field} = :{$field}";
        }

        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE {$where}";
        $params = array_merge($data, $whereParams);

        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
}
?>
```

### 3. 安全类库 (includes/security.php)
```php
<?php
class Security {

    /**
     * 生成API请求签名
     */
    public static function generateSignature($params, $secret, $timestamp) {
        // 移除签名字段
        unset($params['signature']);

        // 参数排序
        ksort($params);

        // 构建查询字符串
        $queryString = http_build_query($params);

        // 添加时间戳和密钥
        $signString = $queryString . '&timestamp=' . $timestamp . '&secret=' . $secret;

        // 生成HMAC-SHA256签名
        return hash_hmac('sha256', $signString, $secret);
    }

    /**
     * 验证API请求签名
     */
    public static function verifySignature($params, $secret, $timestamp, $signature) {
        // 检查时间戳(防重放攻击)
        $currentTime = time();
        if (abs($currentTime - $timestamp) > VERIFY_TIMEOUT) {
            return false;
        }

        // 生成期望的签名
        $expectedSignature = self::generateSignature($params, $secret, $timestamp);

        // 使用时间安全的比较
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 加密数据
     */
    public static function encrypt($data, $key = null) {
        $key = $key ?: ENCRYPTION_KEY;
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * 解密数据
     */
    public static function decrypt($encryptedData, $key = null) {
        $key = $key ?: ENCRYPTION_KEY;
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    /**
     * 生成随机字符串
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * 哈希密码
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID);
    }

    /**
     * 验证密码
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * 清理输入数据
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * 验证IP地址
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * 获取客户端IP
     */
    public static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (self::validateIP($ip)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * 生成设备指纹验证码
     */
    public static function generateDeviceFingerprint($deviceInfo) {
        $data = json_encode($deviceInfo, JSON_SORT_KEYS);
        return hash('sha256', $data);
    }
}
?>
```

## API接口实现

### 1. 在线验证接口 (api/auth/verify.php)
```php
<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';
require_once '../../includes/license.php';
require_once '../../includes/logger.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

    // 必需参数验证
    $requiredParams = ['app_id', 'license_key', 'device_id', 'timestamp', 'signature'];
    foreach ($requiredParams as $param) {
        if (empty($input[$param])) {
            throw new Exception("缺少必需参数: {$param}", 400);
        }
    }

    // 清理输入数据
    $params = Security::sanitizeInput($input);

    // 获取应用信息
    $db = Database::getInstance();
    $app = $db->fetchOne(
        "SELECT * FROM apps WHERE app_id = ? AND status = 1",
        [$params['app_id']]
    );

    if (!$app) {
        throw new Exception("应用不存在或已禁用", 404);
    }

    // 验证请求签名
    if (!Security::verifySignature(
        $params,
        $app['app_secret'],
        $params['timestamp'],
        $params['signature']
    )) {
        throw new Exception("签名验证失败", 401);
    }

    // 验证授权
    $licenseManager = new LicenseManager();
    $verifyResult = $licenseManager->verifyLicense(
        $params['license_key'],
        $params['device_id'],
        $params['app_id'],
        $input
    );

    // 记录验证日志
    Logger::logVerification($params, $verifyResult);

    // 返回结果
    echo json_encode([
        'success' => $verifyResult['success'],
        'message' => $verifyResult['message'],
        'data' => $verifyResult['data'] ?? null,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    $errorCode = $e->getCode() ?: 500;
    http_response_code($errorCode);

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => $errorCode,
        'timestamp' => time()
    ]);

    // 记录错误日志
    Logger::logError($e, $input ?? []);
}
?>
```

### 2. 授权管理类 (includes/license.php)
```php
<?php
class LicenseManager {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * 验证授权许可
     */
    public function verifyLicense($licenseKey, $deviceId, $appId, $requestData = []) {
        try {
            // 查询授权信息
            $license = $this->db->fetchOne(
                "SELECT * FROM licenses WHERE license_key = ? AND app_id = ?",
                [$licenseKey, $appId]
            );

            if (!$license) {
                return [
                    'success' => false,
                    'message' => '授权码不存在',
                    'error_code' => 'LICENSE_NOT_FOUND'
                ];
            }

            // 检查授权状态
            if ($license['status'] === 'banned') {
                return [
                    'success' => false,
                    'message' => '授权已被禁用',
                    'error_code' => 'LICENSE_BANNED'
                ];
            }

            if ($license['status'] === 'expired') {
                return [
                    'success' => false,
                    'message' => '授权已过期',
                    'error_code' => 'LICENSE_EXPIRED'
                ];
            }

            // 检查过期时间
            if ($license['expire_date'] && strtotime($license['expire_date']) < time()) {
                // 更新状态为过期
                $this->db->update('licenses',
                    ['status' => 'expired'],
                    'id = ?',
                    [$license['id']]
                );

                return [
                    'success' => false,
                    'message' => '授权已过期',
                    'error_code' => 'LICENSE_EXPIRED'
                ];
            }

            // 验证设备绑定
            $deviceResult = $this->verifyDevice($license, $deviceId, $requestData);
            if (!$deviceResult['success']) {
                return $deviceResult;
            }

            // 更新最后验证时间
            $this->updateLastVerification($license['id'], $deviceId);

            return [
                'success' => true,
                'message' => '验证成功',
                'data' => [
                    'license_type' => $license['license_type'],
                    'expire_date' => $license['expire_date'],
                    'user_name' => $license['user_name']
                ]
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '验证过程发生错误',
                'error_code' => 'VERIFY_ERROR'
            ];
        }
    }

    /**
     * 验证设备绑定
     */
    private function verifyDevice($license, $deviceId, $requestData) {
        // 查询设备信息
        $device = $this->db->fetchOne(
            "SELECT * FROM devices WHERE device_id = ? AND license_id = ?",
            [$deviceId, $license['id']]
        );

        if ($device) {
            // 设备已存在，检查状态
            if ($device['status'] === 'banned') {
                return [
                    'success' => false,
                    'message' => '设备已被禁用',
                    'error_code' => 'DEVICE_BANNED'
                ];
            }

            // 更新设备信息
            $this->updateDeviceInfo($device['id'], $requestData);

            return ['success' => true];
        }

        // 新设备，检查是否超出限制
        $deviceCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM devices WHERE license_id = ? AND status = 'active'",
            [$license['id']]
        )['count'];

        if ($deviceCount >= $license['max_devices']) {
            return [
                'success' => false,
                'message' => '设备数量已达上限',
                'error_code' => 'DEVICE_LIMIT_EXCEEDED'
            ];
        }

        // 注册新设备
        $this->registerDevice($license['id'], $deviceId, $requestData);

        return ['success' => true];
    }

    /**
     * 注册新设备
     */
    private function registerDevice($licenseId, $deviceId, $requestData) {
        $deviceData = [
            'device_id' => $deviceId,
            'license_id' => $licenseId,
            'device_name' => $requestData['device_name'] ?? 'Unknown Device',
            'os_info' => $requestData['os_info'] ?? '',
            'hardware_info' => json_encode($requestData['hardware_info'] ?? []),
            'first_ip' => Security::getClientIP(),
            'last_ip' => Security::getClientIP(),
            'first_login' => date('Y-m-d H:i:s'),
            'last_login' => date('Y-m-d H:i:s'),
            'login_count' => 1,
            'status' => 'active'
        ];

        $this->db->insert('devices', $deviceData);

        // 更新授权表的已使用设备数
        $this->db->query(
            "UPDATE licenses SET used_devices = used_devices + 1 WHERE id = ?",
            [$licenseId]
        );
    }

    /**
     * 更新设备信息
     */
    private function updateDeviceInfo($deviceId, $requestData) {
        $updateData = [
            'last_ip' => Security::getClientIP(),
            'last_login' => date('Y-m-d H:i:s'),
            'login_count' => new PDO_Expression('login_count + 1')
        ];

        if (!empty($requestData['os_info'])) {
            $updateData['os_info'] = $requestData['os_info'];
        }

        if (!empty($requestData['hardware_info'])) {
            $updateData['hardware_info'] = json_encode($requestData['hardware_info']);
        }

        $this->db->update('devices', $updateData, 'id = ?', [$deviceId]);
    }

    /**
     * 更新最后验证时间
     */
    private function updateLastVerification($licenseId, $deviceId) {
        $this->db->update('licenses', [
            'last_verify_time' => date('Y-m-d H:i:s'),
            'last_verify_ip' => Security::getClientIP(),
            'total_verifications' => new PDO_Expression('total_verifications + 1')
        ], 'id = ?', [$licenseId]);
    }

    /**
     * 生成新的授权码
     */
    public function generateLicense($appId, $licenseType, $expireDays = null, $maxDevices = 1, $userData = []) {
        $licenseKey = Security::generateRandomString(32);

        $expireDate = null;
        if ($expireDays && $licenseType !== 'permanent') {
            $expireDate = date('Y-m-d H:i:s', time() + ($expireDays * 24 * 60 * 60));
        }

        $licenseData = [
            'license_key' => $licenseKey,
            'app_id' => $appId,
            'user_name' => $userData['user_name'] ?? null,
            'user_contact' => $userData['user_contact'] ?? null,
            'license_type' => $licenseType,
            'expire_date' => $expireDate,
            'max_devices' => $maxDevices,
            'status' => 'active',
            'notes' => $userData['notes'] ?? null
        ];

        $licenseId = $this->db->insert('licenses', $licenseData);

        return [
            'license_id' => $licenseId,
            'license_key' => $licenseKey,
            'expire_date' => $expireDate
        ];
    }
}

// PDO表达式类，用于原生SQL表达式
class PDO_Expression {
    private $expression;

    public function __construct($expression) {
        $this->expression = $expression;
    }

    public function __toString() {
        return $this->expression;
    }
}
?>
```

## Python客户端SDK

### 1. 核心SDK类 (security_client.py)
```python
import hashlib
import hmac
import json
import platform
import requests
import subprocess
import time
import uuid
from typing import Dict, Optional, Tuple

class SecurityClient:
    """通用安全框架客户端SDK"""

    def __init__(self, app_id: str, license_key: str, api_base: str = None):
        self.app_id = app_id
        self.license_key = license_key
        self.api_base = api_base or "http://haoke.kechengmao.top/api"
        self.device_id = self._generate_device_id()
        self.app_secret = None  # 将通过配置或首次验证获取

    def verify_license(self) -> Tuple[bool, str]:
        """在线验证授权"""
        try:
            # 反调试检查
            if self._detect_debugger():
                return False, "检测到调试环境"

            # 收集设备信息
            device_info = self._collect_device_info()

            # 构建请求参数
            timestamp = int(time.time())
            params = {
                'app_id': self.app_id,
                'license_key': self.license_key,
                'device_id': self.device_id,
                'timestamp': timestamp,
                'device_name': device_info['device_name'],
                'os_info': device_info['os_info'],
                'hardware_info': device_info['hardware_info']
            }

            # 生成签名（需要app_secret）
            if self.app_secret:
                params['signature'] = self._generate_signature(params, timestamp)

            # 发送验证请求
            response = requests.post(
                f"{self.api_base}/auth/verify.php",
                json=params,
                timeout=30,
                headers={'User-Agent': 'SecurityClient/1.0'}
            )

            if response.status_code == 200:
                result = response.json()
                return result['success'], result['message']
            else:
                return False, f"服务器错误: {response.status_code}"

        except requests.RequestException as e:
            return False, f"网络连接失败: {str(e)}"
        except Exception as e:
            return False, f"验证失败: {str(e)}"

    def _generate_device_id(self) -> str:
        """生成设备唯一标识"""
        try:
            # 获取MAC地址
            mac = hex(uuid.getnode())[2:].upper()

            # 获取CPU信息
            cpu_info = platform.processor() or "unknown"

            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"

            # Windows特有的硬件UUID
            hardware_uuid = "unknown"
            try:
                if platform.system() == "Windows":
                    result = subprocess.check_output(
                        'wmic csproduct get uuid',
                        shell=True,
                        stderr=subprocess.DEVNULL
                    ).decode().strip()
                    lines = result.split('\n')
                    if len(lines) > 1:
                        hardware_uuid = lines[1].strip()
            except:
                pass

            # 组合设备信息
            device_string = f"{mac}-{cpu_info}-{system_info}-{hardware_uuid}"

            # 生成SHA256哈希
            return hashlib.sha256(device_string.encode()).hexdigest()

        except Exception:
            # 如果获取失败，使用随机UUID
            return str(uuid.uuid4()).replace('-', '')

    def _collect_device_info(self) -> Dict:
        """收集设备信息"""
        try:
            return {
                'device_name': platform.node() or "Unknown",
                'os_info': f"{platform.system()} {platform.release()} {platform.version()}",
                'hardware_info': {
                    'processor': platform.processor(),
                    'architecture': platform.architecture(),
                    'machine': platform.machine(),
                    'python_version': platform.python_version()
                }
            }
        except Exception:
            return {
                'device_name': "Unknown",
                'os_info': "Unknown",
                'hardware_info': {}
            }

    def _generate_signature(self, params: Dict, timestamp: int) -> str:
        """生成API请求签名"""
        # 移除签名字段
        sign_params = {k: v for k, v in params.items() if k != 'signature'}

        # 参数排序
        sorted_params = sorted(sign_params.items())

        # 构建查询字符串
        query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])

        # 添加时间戳和密钥
        sign_string = f"{query_string}&timestamp={timestamp}&secret={self.app_secret}"

        # 生成HMAC-SHA256签名
        return hmac.new(
            self.app_secret.encode(),
            sign_string.encode(),
            hashlib.sha256
        ).hexdigest()

    def _detect_debugger(self) -> bool:
        """检测调试器"""
        try:
            import ctypes
            import threading

            # Windows调试器检测
            if platform.system() == "Windows":
                # 检测调试器存在
                if ctypes.windll.kernel32.IsDebuggerPresent():
                    return True

                # 检测远程调试器
                remote_debugger = ctypes.c_bool()
                ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                    ctypes.windll.kernel32.GetCurrentProcess(),
                    ctypes.byref(remote_debugger)
                )
                if remote_debugger.value:
                    return True

            # 时间检测
            start_time = time.time()
            time.sleep(0.01)  # 正常应该是10ms
            end_time = time.time()

            # 如果时间差异过大，可能被调试
            if (end_time - start_time) > 0.1:
                return True

            return False

        except Exception:
            return False

    def check_updates(self) -> Optional[Dict]:
        """检查更新"""
        try:
            params = {
                'app_id': self.app_id,
                'current_version': '1.0.0'  # 应该从配置获取
            }

            response = requests.get(
                f"{self.api_base}/update/check.php",
                params=params,
                timeout=10
            )

            if response.status_code == 200:
                return response.json()

        except Exception:
            pass

        return None

### 2. 集成示例 (好课在线项目集成)
```python
# 在auto_downloader.py中集成安全框架
from security_client import SecurityClient

class AutoHaokeDownloader:
    def __init__(self, config_path="config.json"):
        # 原有初始化代码...
        self.config_path = config_path
        self.config = self.load_config()

        # 新增：安全客户端初始化
        self.security_client = SecurityClient(
            app_id="haoke_downloader",
            license_key=self.config.get("license_key", ""),
            api_base="http://haoke.kechengmao.top/api"
        )

        # 验证授权
        if not self._verify_authorization():
            print("❌ 授权验证失败，程序退出")
            exit(1)

    def _verify_authorization(self):
        """验证授权"""
        print("🔐 正在验证授权...")

        success, message = self.security_client.verify_license()
        if success:
            print("✅ 授权验证成功")
            return True
        else:
            print(f"❌ 授权验证失败: {message}")
            return False

    def run(self):
        """主运行方法"""
        # 在程序开始时再次验证
        if not self._verify_authorization():
            return

        # 检查更新
        self._check_updates()

        # 原有的运行逻辑...
        # ...

    def _check_updates(self):
        """检查更新"""
        try:
            update_info = self.security_client.check_updates()
            if update_info and update_info.get('has_update'):
                print(f"🔄 发现新版本: {update_info['latest_version']}")
                if update_info.get('force_update'):
                    print("⚠️ 强制更新，程序将退出")
                    exit(1)
        except Exception as e:
            print(f"⚠️ 检查更新失败: {str(e)}")
```

### 3. 配置文件更新 (config.json)
```json
{
    "license_key": "your-license-key-here",
    "app_version": "1.0.0",
    "账号列表": [
        // 原有配置...
    ],
    // 其他原有配置...
}
```

## 管理后台开发

### 1. 管理员登录 (admin/login.php)
```php
<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = Security::sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = "请输入用户名和密码";
    } else {
        $db = Database::getInstance();
        $admin = $db->fetchOne(
            "SELECT * FROM admins WHERE username = ? AND status = 1",
            [$username]
        );

        if ($admin && Security::verifyPassword($password, $admin['password'])) {
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_role'] = $admin['role'];

            // 更新登录信息
            $db->update('admins', [
                'last_login' => date('Y-m-d H:i:s'),
                'login_count' => $admin['login_count'] + 1
            ], 'id = ?', [$admin['id']]);

            header('Location: dashboard.php');
            exit;
        } else {
            $error = "用户名或密码错误";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台登录</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 50px; }
        .login-container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { background: #007cba; color: white; padding: 12px 20px; border: none; border-radius: 4px; cursor: pointer; width: 100%; }
        .btn:hover { background: #005a87; }
        .error { color: #d32f2f; margin-bottom: 15px; padding: 10px; background: #ffebee; border-radius: 4px; }
        .title { text-align: center; margin-bottom: 30px; color: #333; }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 class="title">安全框架管理后台</h2>

        <?php if (isset($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required
                       value="<?php echo htmlspecialchars($username ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="btn">登录</button>
        </form>
    </div>
</body>
</html>
```

### 2. 仪表板 (admin/dashboard.php)
```php
<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/database.php';

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();

// 获取统计数据
$stats = [
    'total_licenses' => $db->fetchOne("SELECT COUNT(*) as count FROM licenses")['count'],
    'active_licenses' => $db->fetchOne("SELECT COUNT(*) as count FROM licenses WHERE status = 'active'")['count'],
    'total_devices' => $db->fetchOne("SELECT COUNT(*) as count FROM devices")['count'],
    'today_verifications' => $db->fetchOne("SELECT COUNT(*) as count FROM verify_logs WHERE DATE(created_at) = CURDATE()")['count']
];

// 获取最近的验证日志
$recent_logs = $db->fetchAll(
    "SELECT vl.*, l.user_name FROM verify_logs vl
     LEFT JOIN licenses l ON vl.license_key = l.license_key
     ORDER BY vl.created_at DESC LIMIT 10"
);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 仪表板</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; }
        .nav { background: #34495e; padding: 10px 20px; }
        .nav a { color: white; text-decoration: none; margin-right: 20px; padding: 8px 15px; border-radius: 4px; }
        .nav a:hover, .nav a.active { background: #2c3e50; }
        .container { padding: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #7f8c8d; margin-top: 5px; }
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table-header { background: #34495e; color: white; padding: 15px 20px; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; }
        .status-success { color: #27ae60; }
        .status-failed { color: #e74c3c; }
        .logout { color: white; text-decoration: none; }
    </style>
</head>
<body>
    <div class="header">
        <h1>安全框架管理后台</h1>
        <div>
            欢迎, <?php echo htmlspecialchars($_SESSION['admin_username']); ?> |
            <a href="logout.php" class="logout">退出</a>
        </div>
    </div>

    <div class="nav">
        <a href="dashboard.php" class="active">仪表板</a>
        <a href="licenses.php">授权管理</a>
        <a href="devices.php">设备管理</a>
        <a href="logs.php">日志查看</a>
        <a href="settings.php">系统设置</a>
    </div>

    <div class="container">
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_licenses']; ?></div>
                <div class="stat-label">总授权数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active_licenses']; ?></div>
                <div class="stat-label">活跃授权</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_devices']; ?></div>
                <div class="stat-label">注册设备</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['today_verifications']; ?></div>
                <div class="stat-label">今日验证</div>
            </div>
        </div>

        <div class="table-container">
            <div class="table-header">最近验证记录</div>
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>用户</th>
                        <th>应用ID</th>
                        <th>设备ID</th>
                        <th>IP地址</th>
                        <th>结果</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_logs as $log): ?>
                    <tr>
                        <td><?php echo date('m-d H:i', strtotime($log['created_at'])); ?></td>
                        <td><?php echo htmlspecialchars($log['user_name'] ?: '未知'); ?></td>
                        <td><?php echo htmlspecialchars($log['app_id']); ?></td>
                        <td><?php echo substr($log['device_id'], 0, 8) . '...'; ?></td>
                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                        <td class="<?php echo $log['verify_result'] === 'success' ? 'status-success' : 'status-failed'; ?>">
                            <?php echo $log['verify_result']; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
```

## 部署和使用指南

### 1. 服务器部署步骤

#### 步骤1: 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装LAMP环境
sudo apt install apache2 mysql-server php7.4 php7.4-mysql php7.4-curl php7.4-json -y

# 启用Apache模块
sudo a2enmod rewrite
sudo systemctl restart apache2
```

#### 步骤2: 数据库初始化
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS haoke_kechengmao CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE haoke_kechengmao;

-- 执行所有表结构SQL（从文档中复制）
-- ...

-- 创建默认管理员账户
INSERT INTO admins (username, password, email, role, status) VALUES
('admin', '$argon2id$v=19$m=65536,t=4,p=3$example', '<EMAIL>', 'super', 1);

-- 创建示例应用
INSERT INTO apps (app_id, app_name, app_secret, version, status) VALUES
('haoke_downloader', '好课在线下载器', 'your-app-secret-key-here', '1.0.0', 1);

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('site_name', '安全框架管理系统', '网站名称'),
('max_devices_per_license', '3', '每个授权最大设备数'),
('verify_timeout', '30', '验证超时时间(秒)');
```

#### 步骤3: 代码部署
```bash
# 创建项目目录
sudo mkdir -p /var/www/haoke.kechengmao.top

# 设置权限
sudo chown -R www-data:www-data /var/www/haoke.kechengmao.top
sudo chmod -R 755 /var/www/haoke.kechengmao.top

# 上传代码文件到服务器
# 可以使用FTP、SCP或Git等方式

# 设置日志目录权限
sudo mkdir -p /var/www/haoke.kechengmao.top/logs
sudo chmod 777 /var/www/haoke.kechengmao.top/logs
```

#### 步骤4: Apache配置
```apache
# 创建虚拟主机配置
sudo nano /etc/apache2/sites-available/haoke.kechengmao.top.conf

<VirtualHost *:80>
    ServerName haoke.kechengmao.top
    DocumentRoot /var/www/haoke.kechengmao.top

    <Directory /var/www/haoke.kechengmao.top>
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/haoke_error.log
    CustomLog ${APACHE_LOG_DIR}/haoke_access.log combined
</VirtualHost>

# 启用站点
sudo a2ensite haoke.kechengmao.top.conf
sudo systemctl reload apache2
```

### 2. 客户端集成步骤

#### 步骤1: 安装SDK
```bash
# 将security_client.py复制到项目目录
cp security_client.py /path/to/your/project/

# 安装依赖
pip install requests
```

#### 步骤2: 修改现有项目
```python
# 在auto_downloader.py开头添加
from security_client import SecurityClient

# 在__init__方法中添加安全验证
def __init__(self, config_path="config.json"):
    # 原有代码...

    # 添加安全验证
    license_key = self.config.get("license_key")
    if not license_key:
        print("❌ 请在配置文件中设置license_key")
        exit(1)

    self.security_client = SecurityClient(
        app_id="haoke_downloader",
        license_key=license_key
    )

    # 验证授权
    success, message = self.security_client.verify_license()
    if not success:
        print(f"❌ 授权验证失败: {message}")
        exit(1)

    print("✅ 授权验证成功")
```

#### 步骤3: 更新配置文件
```json
{
    "license_key": "请联系管理员获取授权码",
    "app_version": "1.0.0",
    "账号列表": [
        // 原有配置保持不变
    ]
}
```

### 3. 授权码生成和管理

#### 通过管理后台生成
1. 访问 http://haoke.kechengmao.top/admin/
2. 使用管理员账户登录
3. 进入"授权管理"页面
4. 点击"新增授权"
5. 填写用户信息和授权类型
6. 生成授权码并提供给用户

#### 通过API生成（开发用）
```php
// 示例：生成测试授权码
$licenseManager = new LicenseManager();
$result = $licenseManager->generateLicense(
    'haoke_downloader',  // 应用ID
    'monthly',           // 授权类型
    30,                  // 有效期天数
    1,                   // 最大设备数
    [
        'user_name' => '测试用户',
        'user_contact' => '<EMAIL>',
        'notes' => '测试授权'
    ]
);

echo "授权码: " . $result['license_key'];
```

这个完整的开发文档涵盖了从数据库设计到部署上线的所有环节。按照这个文档，您可以系统性地实现整个安全框架，并成功集成到好课在线项目中。
```
```
