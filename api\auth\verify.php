<?php
/**
 * 通用安全框架 - 在线验证接口
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 定义框架常量
define('SECURITY_FRAMEWORK', true);

// 引入核心文件
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';
require_once '../../includes/license.php';
require_once '../../includes/logger.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('只允许POST请求', 405, 'METHOD_NOT_ALLOWED');
}

try {
    // 获取客户端IP
    $clientIP = getClientIP();
    
    // 检查请求频率限制
    if (!Security::checkRateLimit($clientIP)) {
        errorResponse('请求过于频繁，请稍后重试', 429, 'RATE_LIMIT_EXCEEDED');
    }
    
    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    // 如果JSON解析失败，尝试解析表单数据
    if ($input === null) {
        $input = $_POST;
    }
    
    if (empty($input)) {
        errorResponse('请求数据为空', 400, 'EMPTY_REQUEST');
    }
    
    // 记录原始请求（调试模式）
    if (DEBUG_MODE) {
        writeLog('DEBUG', 'Verification request received', [
            'client_ip' => $clientIP,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_size' => strlen($rawInput),
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? ''
        ]);
    }
    
    // 验证必需参数
    $requiredParams = ['app_id', 'license_key', 'device_id', 'timestamp'];
    $missingParams = [];
    
    foreach ($requiredParams as $param) {
        if (!isset($input[$param]) || $input[$param] === '') {
            $missingParams[] = $param;
        }
    }
    
    if (!empty($missingParams)) {
        errorResponse('缺少必需参数: ' . implode(', ', $missingParams), 400, 'MISSING_PARAMETERS');
    }
    
    // 清理输入数据
    $params = Security::sanitizeInput($input);
    
    // 验证时间戳格式
    if (!is_numeric($params['timestamp']) || $params['timestamp'] <= 0) {
        errorResponse('无效的时间戳格式', 400, 'INVALID_TIMESTAMP');
    }
    
    // 验证设备ID格式
    if (strlen($params['device_id']) < 16 || strlen($params['device_id']) > 128) {
        errorResponse('无效的设备ID格式', 400, 'INVALID_DEVICE_ID');
    }
    
    // 验证授权码格式
    if (strlen($params['license_key']) < 16 || strlen($params['license_key']) > 64) {
        errorResponse('无效的授权码格式', 400, 'INVALID_LICENSE_KEY');
    }
    
    // 获取应用信息
    $db = Database::getInstance();
    $app = $db->fetchOne(
        "SELECT * FROM apps WHERE app_id = ? AND status = 1",
        [$params['app_id']]
    );
    
    if (!$app) {
        writeLog('WARNING', 'Unknown app access attempt', [
            'app_id' => $params['app_id'],
            'client_ip' => $clientIP,
            'license_key' => substr($params['license_key'], 0, 8) . '...'
        ]);
        errorResponse('应用不存在或已禁用', 404, 'APP_NOT_FOUND');
    }
    
    // 验证请求签名（如果提供）
    if (isset($params['signature'])) {
        if (!Security::verifySignature(
            $params, 
            $app['app_secret'], 
            $params['timestamp'], 
            $params['signature']
        )) {
            writeLog('WARNING', 'Signature verification failed', [
                'app_id' => $params['app_id'],
                'client_ip' => $clientIP,
                'license_key' => substr($params['license_key'], 0, 8) . '...',
                'device_id' => substr($params['device_id'], 0, 8) . '...'
            ]);
            errorResponse('签名验证失败', 401, 'SIGNATURE_VERIFICATION_FAILED');
        }
    } else {
        // 如果没有签名，记录警告（但不阻止验证，向后兼容）
        writeLog('WARNING', 'Request without signature', [
            'app_id' => $params['app_id'],
            'client_ip' => $clientIP
        ]);
    }
    
    // 执行授权验证
    $licenseManager = new LicenseManager();
    $verifyResult = $licenseManager->verifyLicense(
        $params['license_key'],
        $params['device_id'],
        $params['app_id'],
        $input // 传递原始输入数据
    );
    
    // 记录验证结果
    writeLog('INFO', 'License verification completed', [
        'app_id' => $params['app_id'],
        'license_key' => substr($params['license_key'], 0, 8) . '...',
        'device_id' => substr($params['device_id'], 0, 8) . '...',
        'client_ip' => $clientIP,
        'result' => $verifyResult['success'] ? 'success' : 'failed',
        'error_code' => $verifyResult['error_code'] ?? null
    ]);
    
    // 构建响应数据
    $responseData = [
        'success' => $verifyResult['success'],
        'message' => $verifyResult['message']
    ];
    
    // 添加成功时的额外数据
    if ($verifyResult['success'] && isset($verifyResult['data'])) {
        $responseData['data'] = $verifyResult['data'];
        
        // 添加服务器时间和应用信息
        $responseData['server_time'] = time();
        $responseData['app_version'] = $app['version'];
        
        // 检查是否需要更新
        if (isset($input['client_version']) && version_compare($input['client_version'], $app['version'], '<')) {
            $responseData['update_available'] = true;
            $responseData['latest_version'] = $app['version'];
            
            if ($app['force_update']) {
                $responseData['force_update'] = true;
                $responseData['update_message'] = $app['update_message'] ?? '发现新版本，请立即更新';
            }
        }
    }
    
    // 返回结果
    if ($verifyResult['success']) {
        successResponse($responseData);
    } else {
        // 验证失败时的HTTP状态码映射
        $httpCode = 403; // 默认禁止访问
        
        switch ($verifyResult['error_code'] ?? '') {
            case 'LICENSE_NOT_FOUND':
                $httpCode = 404;
                break;
            case 'LICENSE_EXPIRED':
            case 'LICENSE_BANNED':
                $httpCode = 410; // Gone
                break;
            case 'DEVICE_LIMIT_EXCEEDED':
            case 'DEVICE_BANNED':
                $httpCode = 429; // Too Many Requests
                break;
            default:
                $httpCode = 403;
        }
        
        errorResponse($verifyResult['message'], $httpCode, $verifyResult['error_code'] ?? 'VERIFICATION_FAILED');
    }
    
} catch (Exception $e) {
    // 记录异常
    writeLog('ERROR', 'Verification API error', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'client_ip' => getClientIP(),
        'request_data' => $input ?? []
    ]);
    
    // 返回通用错误（不暴露内部错误信息）
    if (DEBUG_MODE) {
        errorResponse('内部服务器错误: ' . $e->getMessage(), 500, 'INTERNAL_SERVER_ERROR');
    } else {
        errorResponse('服务暂时不可用，请稍后重试', 500, 'SERVICE_UNAVAILABLE');
    }
}

?>
