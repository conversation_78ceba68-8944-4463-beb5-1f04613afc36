#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用安全框架 - Python客户端SDK

<AUTHOR> Framework Team
@version 1.0.0
@date 2024-08-08
"""

import hashlib
import hmac
import json
import platform
import requests
import subprocess
import time
import uuid
import sys
import os
from typing import Dict, Optional, Tuple, Any

class SecurityClient:
    """通用安全框架客户端SDK"""
    
    def __init__(self, app_id: str, license_key: str, api_base: str = None, app_secret: str = None):
        """
        初始化安全客户端
        
        Args:
            app_id: 应用ID
            license_key: 授权码
            api_base: API基础URL
            app_secret: 应用密钥（用于签名）
        """
        self.app_id = app_id
        self.license_key = license_key
        self.api_base = api_base or "http://haoke.kechengmao.top/api"
        self.app_secret = app_secret
        self.device_id = self._generate_device_id()
        self.client_version = "1.0.0"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': f'SecurityClient/{self.client_version} ({platform.system()} {platform.release()})',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # 设置超时
        self.session.timeout = 30
        
    def verify_license(self) -> Tuple[bool, str, Optional[Dict]]:
        """
        在线验证授权
        
        Returns:
            Tuple[bool, str, Optional[Dict]]: (成功状态, 消息, 数据)
        """
        try:
            # 反调试检查
            if self._detect_debugger():
                return False, "检测到调试环境，程序退出", None
            
            # 收集设备信息
            device_info = self._collect_device_info()
            
            # 构建请求参数
            timestamp = int(time.time())
            params = {
                'app_id': self.app_id,
                'license_key': self.license_key,
                'device_id': self.device_id,
                'timestamp': timestamp,
                'client_version': self.client_version,
                'device_name': device_info['device_name'],
                'os_info': device_info['os_info'],
                'hardware_info': device_info['hardware_info']
            }
            
            # 生成签名（如果有密钥）
            if self.app_secret:
                params['signature'] = self._generate_signature(params, timestamp)
            
            # 发送验证请求
            response = self.session.post(
                f"{self.api_base}/auth/verify.php",
                json=params
            )
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                return result['success'], result['message'], result.get('data')
            else:
                try:
                    error_data = response.json()
                    return False, error_data.get('message', f'服务器错误: {response.status_code}'), None
                except:
                    return False, f'服务器错误: {response.status_code}', None
                    
        except requests.RequestException as e:
            return False, f"网络连接失败: {str(e)}", None
        except Exception as e:
            return False, f"验证失败: {str(e)}", None
    
    def _generate_device_id(self) -> str:
        """生成设备唯一标识"""
        try:
            # 获取MAC地址
            mac = hex(uuid.getnode())[2:].upper()
            
            # 获取CPU信息
            cpu_info = platform.processor() or "unknown"
            
            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"
            
            # Windows特有的硬件UUID
            hardware_uuid = "unknown"
            try:
                if platform.system() == "Windows":
                    result = subprocess.check_output(
                        'wmic csproduct get uuid', 
                        shell=True, 
                        stderr=subprocess.DEVNULL,
                        timeout=10
                    ).decode().strip()
                    lines = result.split('\n')
                    if len(lines) > 1:
                        hardware_uuid = lines[1].strip()
            except:
                pass
            
            # 组合设备信息
            device_string = f"{mac}-{cpu_info}-{system_info}-{hardware_uuid}"
            
            # 生成SHA256哈希
            return hashlib.sha256(device_string.encode()).hexdigest()
            
        except Exception:
            # 如果获取失败，使用随机UUID
            return str(uuid.uuid4()).replace('-', '')
    
    def _collect_device_info(self) -> Dict[str, Any]:
        """收集设备信息"""
        try:
            return {
                'device_name': platform.node() or "Unknown",
                'os_info': f"{platform.system()} {platform.release()} {platform.version()}",
                'hardware_info': {
                    'processor': platform.processor(),
                    'architecture': platform.architecture(),
                    'machine': platform.machine(),
                    'python_version': platform.python_version(),
                    'python_implementation': platform.python_implementation()
                }
            }
        except Exception:
            return {
                'device_name': "Unknown",
                'os_info': "Unknown",
                'hardware_info': {}
            }
    
    def _generate_signature(self, params: Dict, timestamp: int) -> str:
        """生成API请求签名"""
        if not self.app_secret:
            raise ValueError("App secret is required for signature generation")
        
        # 移除签名字段
        sign_params = {k: v for k, v in params.items() if k != 'signature'}
        
        # 参数排序
        sorted_params = sorted(sign_params.items())
        
        # 构建查询字符串
        query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        
        # 添加时间戳和密钥
        sign_string = f"{query_string}&timestamp={timestamp}&secret={self.app_secret}"
        
        # 生成HMAC-SHA256签名
        return hmac.new(
            self.app_secret.encode(),
            sign_string.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def _detect_debugger(self) -> bool:
        """检测调试器"""
        try:
            # Windows调试器检测
            if platform.system() == "Windows":
                try:
                    import ctypes
                    
                    # 检测调试器存在
                    if ctypes.windll.kernel32.IsDebuggerPresent():
                        return True
                    
                    # 检测远程调试器
                    remote_debugger = ctypes.c_bool()
                    ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                        ctypes.windll.kernel32.GetCurrentProcess(),
                        ctypes.byref(remote_debugger)
                    )
                    if remote_debugger.value:
                        return True
                except:
                    pass
            
            # 检测Python调试器
            if hasattr(sys, 'gettrace') and sys.gettrace() is not None:
                return True
            
            # 时间检测
            start_time = time.time()
            time.sleep(0.01)  # 正常应该是10ms
            end_time = time.time()
            
            # 如果时间差异过大，可能被调试
            if (end_time - start_time) > 0.1:
                return True
            
            # 检测常见调试工具进程
            debug_processes = [
                'ollydbg.exe', 'x64dbg.exe', 'windbg.exe', 'ida.exe', 'ida64.exe',
                'cheatengine.exe', 'processhacker.exe', 'procmon.exe'
            ]
            
            try:
                if platform.system() == "Windows":
                    import psutil
                    running_processes = [p.name().lower() for p in psutil.process_iter(['name'])]
                    for debug_proc in debug_processes:
                        if debug_proc.lower() in running_processes:
                            return True
            except:
                pass
            
            return False
            
        except Exception:
            return False
    
    def check_updates(self) -> Optional[Dict]:
        """检查更新"""
        try:
            params = {
                'app_id': self.app_id,
                'current_version': self.client_version
            }
            
            response = self.session.get(
                f"{self.api_base}/update/check.php",
                params=params
            )
            
            if response.status_code == 200:
                return response.json()
            
        except Exception:
            pass
        
        return None
    
    def heartbeat(self) -> bool:
        """发送心跳检测"""
        try:
            params = {
                'app_id': self.app_id,
                'license_key': self.license_key,
                'device_id': self.device_id,
                'timestamp': int(time.time())
            }
            
            if self.app_secret:
                params['signature'] = self._generate_signature(params, params['timestamp'])
            
            response = self.session.post(
                f"{self.api_base}/auth/heartbeat.php",
                json=params
            )
            
            return response.status_code == 200
            
        except Exception:
            return False
    
    def get_device_id(self) -> str:
        """获取设备ID"""
        return self.device_id
    
    def set_app_secret(self, app_secret: str):
        """设置应用密钥"""
        self.app_secret = app_secret
    
    def set_client_version(self, version: str):
        """设置客户端版本"""
        self.client_version = version


# 便捷函数
def create_security_client(app_id: str, license_key: str, **kwargs) -> SecurityClient:
    """创建安全客户端实例"""
    return SecurityClient(app_id, license_key, **kwargs)


def verify_license_simple(app_id: str, license_key: str, api_base: str = None) -> bool:
    """简单的授权验证函数"""
    client = SecurityClient(app_id, license_key, api_base)
    success, message, data = client.verify_license()
    return success


# 示例用法
if __name__ == "__main__":
    # 测试代码
    client = SecurityClient(
        app_id="haoke_downloader",
        license_key="test_license_key_for_development_only_2024",
        api_base="http://haoke.kechengmao.top/api"
    )
    
    print(f"设备ID: {client.get_device_id()}")
    
    success, message, data = client.verify_license()
    print(f"验证结果: {success}")
    print(f"消息: {message}")
    if data:
        print(f"数据: {data}")
