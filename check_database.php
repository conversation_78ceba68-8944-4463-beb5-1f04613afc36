<?php
/**
 * 检查数据库数据
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=haoke_kechengmao;charset=utf8mb4",
        "haoke_kechengmao",
        "9Fyc8e9HRXbDbzmX"
    );
    
    $result = [
        'timestamp' => time(),
        'database_check' => 'success'
    ];
    
    // 检查apps表
    $stmt = $pdo->query("SELECT * FROM apps");
    $apps = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['apps'] = $apps;
    
    // 检查licenses表
    $stmt = $pdo->query("SELECT * FROM licenses");
    $licenses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['licenses'] = $licenses;
    
    // 检查devices表
    $stmt = $pdo->query("SELECT * FROM devices");
    $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['devices'] = $devices;
    
    // 检查特定的测试数据
    $stmt = $pdo->prepare("SELECT * FROM licenses WHERE license_key = ? AND app_id = ?");
    $stmt->execute(['test_license_key_for_development_only_2024', 'haoke_downloader']);
    $testLicense = $stmt->fetch(PDO::FETCH_ASSOC);
    $result['test_license'] = $testLicense;
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
