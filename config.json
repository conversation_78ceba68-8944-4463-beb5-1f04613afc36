{"license_key": "test_license_key_for_development_only_2024", "app_version": "1.0.0", "账号列表": [{"账号名称": "", "用户名": "18786283303", "密码": "wug954808", "课程ID列表": ["3281837", "3281844", "3283372", "3283366"], "启用": true, "备注": ""}, {"账号名称": "", "用户名": "15585410668", "密码": "002668shuo", "课程ID列表": ["3281785", "3283305"], "启用": true, "备注": ""}], "执行模式": {"模式": "选择账号", "说明": "可选值: 选择账号(手动选择) | 依次执行(按顺序执行所有启用账号)"}, "课程配置": {"只下载系统课": true, "课程说明": "每个账号可以有独立的课程ID列表"}, "课程前缀筛选": ["【2025暑】"], "课程前缀筛选说明": "只下载课程名以这些前缀开头的课程，留空或删除本项则全部处理，支持多个前缀，如[\"【2025暑】\",\"【2025秋】\"]", "下载设置": {"下载内容选择": 2, "下载内容说明": "0=全部内容(视频+独立课程资料) | 1=仅视频(包含随堂资料) | 2=仅课程资料", "下载后创建占位文件": true, "自动下载全部章节": true, "下载视频和资料": true, "最大并发下载数": 16, "下载重试次数": 3, "启用多线程下载": true, "多线程下载线程数": 8, "多线程下载说明": "启用多线程分块下载可显著提高大文件下载速度，类似IDM加速效果"}, "文件夹设置": {"真实文件目录": "downloads", "占位文件目录": "placeholders", "Cookie文件目录": "cookies"}, "课程信息映射": {"说明": "课程ID和课程名称的对应关系，程序运行时自动收集", "3281837": "【2025秋】高考数学目标清北班（一轮）-谭梦云", "3281844": "【2025秋】高考物理目标双一流班（一轮）-蔺天威", "3283372": "【2025暑】高考物理目标双一流班（一轮）·2期-蔺天威", "3283366": "【2025暑】高考数学目标清北班（一轮）·2期-谭梦云", "3281504": "【2025暑】新高一英语目标双一流班·3期-何红艳", "3281520": "【2025暑】新高一语文目标双一流班·3期-罗斐然", "3281482": "【2025暑】新高一数学目标双一流班·4期-祖少磊", "3281759": "【2025秋】新高一英语目标一本班-何红艳", "3281674": "【2025秋】新高一数学目标双一流班-祖少磊", "3281768": "【2025秋】新高一语文目标双一流班-罗斐然", "3281516": "【2025暑】新高一语文目标双一流班·4期-陈晨", "3281532": "【2025暑】新高一政治系统班·4期", "3281472": "【2025暑】新高一数学目标清北班·2期-韩佳伟", "3281490": "【2025暑】新高一物理目标双一流班·1期-宋雨晴", "3281525": "【2025暑】新高一生物目标双一流班·1期-周云", "3281507": "【2025暑】新高一英语目标清北班·2期", "3281496": "【2025暑】新高一物理目标清北班·2期-俞伯勋", "3281517": "【2025暑】新高一语文目标清北班·3期", "3281479": "【2025暑】新高一数学目标双一流班·4期-徐迅", "3281771": "【2025秋】新高一生物目标双一流班-周云", "3281684": "【2025秋】新高一物理目标清北班-俞伯勋", "3281767": "【2025秋】新高一语文目标清北班-罗斐然", "3281760": "【2025秋】新高一英语目标清北班-卫宇晴", "3281672": "【2025秋】新高一数学目标双一流班-徐迅", "3281529": "【2025暑】新高一历史系统班·4期-刘莹莹", "3281531": "【2025暑】新高一地理系统班·4期-王媛韬", "3281667": "【2025秋】新高一数学目标清北班-韩佳伟", "3281774": "【2025秋】新高一历史系统班-刘莹莹", "3281775": "【2025秋】新高一地理系统班-王媛韬", "3283347": "【2025暑】新高二生物目标双一流班·1期-谢一凡", "3283337": "【2025暑】新高二英语目标双一流班·1期-袁慧", "3283316": "【2025暑】新高二物理目标双一流班·1期-何连伟", "3283345": "【2025暑】新高二语文目标双一流班·2期-李秋颖", "3283327": "【2025暑】新高二化学目标双一流班（反应原理）·2期-康冲", "3283309": "【2025暑】新高二数学目标双一流班·2期-腾毅", "3281819": "【2025秋】新高二生物目标双一流班-谢一凡", "3281812": "【2025秋】新高二英语目标双一流班-袁慧", "3281790": "【2025秋】新高二数学目标双一流班-腾毅", "3281803": "【2025秋】新高二化学目标双一流班（反应原理+结构）-康冲", "3281794": "【2025秋】新高二物理目标双一流班（必修3+选修2）-何连伟", "3281818": "【2025秋】新高二语文目标双一流班-李秋颖", "3281481": "【2025暑】新高一数学目标双一流班·2期-祖少磊", "3283367": "【2025暑】高考数学目标双一流班（一轮）·1期-谭梦云", "3281851": "【2025秋】高考物理目标清北班（一轮）-袁帅", "3281838": "【2025秋】高考数学目标双一流班（一轮）-谭梦云", "3283396": "【2025暑】高考英语目标一本班（一轮）·2期-李播恩", "3281867": "【2025秋】高考英语目标一本班（一轮）-李播恩", "3283414": "【2025暑】高考地理系统班（一轮）·1期-王群", "3283381": "【2025暑】高考物理目标双一流班（一轮）·2期-郑梦瑶", "3283405": "【2025暑】高考语文目标双一流班（一轮）·2期-张亚柔", "3281885": "【2025秋】高考地理系统班（一轮）-王群", "3281852": "【2025秋】高考物理目标双一流班（一轮）-郑梦瑶", "3281878": "【2025秋】高考语文目标双一流班（一轮）-张亚柔", "3283392": "【2025暑】高考英语目标双一流班（一轮）·1期-古容容", "3283400": "【2025暑】高考语文目标清北班（一轮）·1期-李建华", "3281857": "【2025秋】高考化学目标双一流班（一轮）-林森", "3281871": "【2025秋】高考语文目标清北班（一轮）-李建华", "3281879": "【2025秋】高考生物目标双一流班（一轮）-邓康尧", "3281862": "【2025秋】高考英语目标双一流班（一轮）-古容容", "3283365": "【2025暑】高考数学目标一本班（一轮）·2期-刘天麒", "3281836": "【2025秋】高考数学目标一本班（一轮）-刘天麒", "3281849": "【2025秋】高考物理目标双一流班（一轮）-杨会英", "3283378": "【2025暑】高考物理目标双一流班（一轮）·1期-杨会英", "3283386": "【2025暑】高考化学目标清北班（一轮）·2期-林森", "3281856": "【2025秋】高考化学目标清北班（一轮）-林森", "3283395": "【2025暑】高考英语目标双一流班（一轮）·1期-李播恩", "3283363": "【2025暑】高考数学目标双一流班（一轮）·1期-刘天麒", "3281835": "【2025秋】高考数学目标双一流班（一轮）-刘天麒", "3281865": "【2025秋】高考英语目标双一流班（一轮）-李播恩", "3283406": "【2025暑】高考生物目标双一流班（一轮）·1期-邓康尧", "3283380": "【2025暑】高考物理目标清北班（一轮）·2期-袁帅", "3283404": "【2025暑】高考语文目标清北班（一轮）·2期-张亚柔", "3281875": "【2025秋】高考语文目标清北班（一轮）-张亚柔", "3283376": "【2025暑】高考物理目标一本班（一轮）·2期-鲜朝阳", "3281847": "【2025秋】高考物理目标一本班（一轮）-鲜朝阳", "3283361": "【2025暑】高考数学系统班（一轮）·1期-刘秋龙", "3283387": "【2025暑】高考化学目标双一流班（一轮）·2期-林森", "3281834": "【2025秋】高考数学系统班（一轮）-刘秋龙", "3283320": "【2025暑】新高二物理目标一本班·2期-庞海亮", "3283328": "【2025暑】新高二化学目标一本班（反应原理）·2期-康冲", "3281804": "【2025秋】新高二化学目标一本班（反应原理+结构）-康冲", "3281780": "【2025秋】新高二数学目标一本班-林泽田", "3281797": "【2025秋】新高二物理目标一本班（必修3+选修2）-庞海亮", "3281881": "【2025秋】高考生物目标一本班（一轮）-邓康尧", "3283407": "【2025暑】高考生物目标一本班（一轮）·2期-邓康尧", "3283313": "【2025暑】新高二物理目标双一流班·2期-龚昱晗", "3281792": "【2025秋】新高二物理目标双一流班（必修3+选修2）-龚昱晗", "3283314": "【2025暑】新高二物理目标一本班·1期-龚昱晗", "3283305": "【2025暑】新高二数学目标一本班·1期-田夏林", "3281793": "【2025秋】新高二物理目标一本班（必修3+选修2）-龚昱晗", "3281785": "【2025秋】新高二数学目标一本班-田夏林", "3281795": "【2025秋】新高二物理目标一本班（必修3+选修2）-何连伟", "3283317": "【2025暑】新高二物理目标一本班·2期-何连伟", "3281840": "【2025秋】高考数学目标一本班（一轮）-谭梦云", "3283368": "【2025暑】高考数学目标一本班（一轮）·2期-谭梦云", "3281883": "【2025秋】高考生物目标一本班（一轮）-杨雪", "3283410": "【2025暑】高考生物目标一本班（一轮）·1期-杨雪", "3281816": "【2025秋】新高二语文目标双一流班-郭笑", "3281809": "【2025秋】新高二英语目标双一流班-聂宁", "3283334": "【2025暑】新高二英语目标双一流班·2期-聂宁", "3283343": "【2025暑】新高二语文目标双一流班·1期-郭笑", "3283364": "【2025暑】高考数学目标一本班（一轮）·1期-刘天麒", "3283326": "【2025暑】新高二化学目标双一流班（反应原理）·1期-康冲", "3402761": "【2025暑】高考化学目标双一流班（一轮）·1期-林森", "3283297": "【2025暑】新高二数学目标一本班·1期-倪港钧", "3283338": "【2025暑】新高二英语目标一本班·2期-袁慧", "3281778": "【2025秋】新高二数学目标一本班-倪港钧", "3281813": "【2025秋】新高二英语目标一本班-袁慧", "3283373": "【2025暑】高考物理目标一本班（一轮）·1期-蔺天威", "3283362": "【2025暑】高考数学系统班（一轮）·2期-刘秋龙", "3281845": "【2025秋】高考物理目标一本班（一轮）-蔺天威", "3281858": "【2025秋】高考化学目标一本班（一轮）-林森", "3283298": "【2025暑】新高二数学目标双一流班·1期-林泽田", "3281779": "【2025秋】新高二数学目标双一流班-林泽田", "3281860": "【2025秋】高考化学目标双一流班（一轮）-赵怡然", "3283390": "【2025暑】高考化学目标双一流班（一轮）·1期-赵怡然", "3283411": "【2025暑】高考历史系统班（一轮）·2期-刘莹莹", "3281991": "【2025秋】高考历史系统班（一轮）-刘莹莹", "3283301": "【2025暑】新高二数学系统班·2期-刘秋龙", "3281781": "【2025秋】新高二数学系统班-刘秋龙", "3283374": "【2025暑】高考物理目标双一流班（一轮）·1期-鲜朝阳", "3281846": "【2025秋】高考物理目标双一流班（一轮）-鲜朝阳", "3283384": "【2025暑】高考化学目标双一流班（一轮）·1期-李伟", "3281854": "【2025秋】高考化学目标双一流班（一轮）-李伟", "3281510": "【2025暑】新高一英语目标双一流班·2期-张亮", "3281515": "【2025暑】新高一语文目标双一流班·2期-陈晨", "3281533": "【2025暑】新高一政治系统班·2期-周峤矞", "3288512": "【2025暑】新高一化学系统班（必修1）·2期-成功", "3281526": "【2025暑】新高一生物目标双一流班·3期-周云", "3281491": "【2025暑】新高一物理目标双一流班·3期-宋雨晴", "3281475": "【2025暑】新高一数学目标双一流班·3期-韩佳伟", "3281776": "【2025秋】新高一政治系统班-周峤矞", "3281680": "【2025秋】新高一物理目标双一流班-宋雨晴", "3288504": "【2025秋】新高一化学系统班（必修1）-成功", "3281766": "【2025秋】新高一语文目标双一流班-陈晨", "3281763": "【2025秋】新高一英语目标双一流班-张亮", "3283382": "【2025暑】高考物理目标一本班（一轮）·1期-郑梦瑶", "3283388": "【2025暑】高考化学目标一本班（一轮）·1期-林森", "3281853": "【2025秋】高考物理目标一本班（一轮）-郑梦瑶", "3283408": "【2025暑】高考生物目标双一流班（一轮）·1期-杨雪", "3281882": "【2025秋】高考生物目标双一流班（一轮）-杨雪", "3283403": "【2025暑】高考语文目标双一流班（一轮）·1期-张炯一", "3281874": "【2025秋】高考语文目标双一流班（一轮）-张炯一", "3281505": "【2025暑】新高一英语目标双一流班·1期-何红艳", "3283394": "【2025暑】高考英语目标清北班（一轮）·1期-李播恩"}, "章节连续失败跳过阈值": 2, "章节连续失败跳过阈值说明": "单个课程内连续失败达到该次数后，自动跳过剩余章节，防止未开课章节反复请求。"}