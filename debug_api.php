<?php
/**
 * API调试文件
 * 用于诊断API接口问题
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$debug_info = [
    'timestamp' => time(),
    'date' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
];

// 测试文件包含
$debug_info['file_tests'] = [];

// 测试config.php
$config_path = __DIR__ . '/includes/config.php';
$debug_info['file_tests']['config_exists'] = file_exists($config_path);
if (file_exists($config_path)) {
    $debug_info['file_tests']['config_readable'] = is_readable($config_path);
    if (is_readable($config_path)) {
        try {
            // 定义常量避免错误
            if (!defined('SECURITY_FRAMEWORK')) {
                define('SECURITY_FRAMEWORK', true);
            }
            include_once $config_path;
            $debug_info['file_tests']['config_included'] = true;
            $debug_info['file_tests']['db_config'] = [
                'DB_HOST' => defined('DB_HOST') ? DB_HOST : 'Not defined',
                'DB_NAME' => defined('DB_NAME') ? DB_NAME : 'Not defined',
                'DB_USER' => defined('DB_USER') ? DB_USER : 'Not defined'
            ];
        } catch (Exception $e) {
            $debug_info['file_tests']['config_error'] = $e->getMessage();
        }
    }
}

// 测试database.php
$database_path = __DIR__ . '/includes/database.php';
$debug_info['file_tests']['database_exists'] = file_exists($database_path);
if (file_exists($database_path)) {
    $debug_info['file_tests']['database_readable'] = is_readable($database_path);
    if (is_readable($database_path)) {
        try {
            include_once $database_path;
            $debug_info['file_tests']['database_included'] = true;
        } catch (Exception $e) {
            $debug_info['file_tests']['database_error'] = $e->getMessage();
        }
    }
}

// 测试其他核心文件
$core_files = ['security.php', 'license.php', 'logger.php'];
foreach ($core_files as $file) {
    $file_path = __DIR__ . '/includes/' . $file;
    $debug_info['file_tests'][$file] = [
        'exists' => file_exists($file_path),
        'readable' => file_exists($file_path) ? is_readable($file_path) : false
    ];
}

// 测试数据库连接
if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS
        );
        $debug_info['database_connection'] = 'Success';
        
        // 测试表是否存在
        $tables = ['apps', 'licenses', 'devices', 'verify_logs', 'system_config', 'admins'];
        $debug_info['database_tables'] = [];
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                $debug_info['database_tables'][$table] = "Exists (count: $count)";
            } catch (Exception $e) {
                $debug_info['database_tables'][$table] = "Error: " . $e->getMessage();
            }
        }
        
    } catch (Exception $e) {
        $debug_info['database_connection'] = 'Failed: ' . $e->getMessage();
    }
} else {
    $debug_info['database_connection'] = 'Config not loaded';
}

// 测试logs目录
$logs_dir = __DIR__ . '/logs';
$debug_info['logs_directory'] = [
    'exists' => is_dir($logs_dir),
    'writable' => is_dir($logs_dir) ? is_writable($logs_dir) : false,
    'permissions' => is_dir($logs_dir) ? substr(sprintf('%o', fileperms($logs_dir)), -4) : 'N/A'
];

// 测试API文件
$api_file = __DIR__ . '/api/auth/verify.php';
$debug_info['api_file'] = [
    'exists' => file_exists($api_file),
    'readable' => file_exists($api_file) ? is_readable($api_file) : false,
    'path' => $api_file
];

// 如果是POST请求，尝试模拟API调用
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $debug_info['post_test'] = [];
    
    if (file_exists($api_file) && is_readable($api_file)) {
        try {
            // 捕获输出
            ob_start();
            
            // 模拟POST数据
            $_POST = [
                'app_id' => 'haoke_downloader',
                'license_key' => 'test_license_key_for_development_only_2024',
                'device_id' => 'debug_device_12345',
                'timestamp' => time()
            ];
            
            // 包含API文件
            include $api_file;
            
            $api_output = ob_get_clean();
            $debug_info['post_test']['api_output'] = $api_output;
            $debug_info['post_test']['success'] = true;
            
        } catch (Exception $e) {
            ob_end_clean();
            $debug_info['post_test']['error'] = $e->getMessage();
            $debug_info['post_test']['success'] = false;
        }
    } else {
        $debug_info['post_test']['error'] = 'API file not accessible';
    }
}

// 输出调试信息
echo json_encode($debug_info, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
