#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_debug_verify():
    """测试调试版验证API"""
    print("🔍 测试调试版验证API...")
    
    # 测试数据
    test_data = {
        'app_id': 'haoke_downloader',
        'license_key': 'test_license_key_for_development_only_2024',
        'device_id': 'test_device_12345',
        'timestamp': **********
    }
    
    try:
        response = requests.post(
            "https://haoke.kechengmao.top/debug_verify.php",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 调试API访问成功")
            print(f"调试信息:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"❌ 调试API返回错误状态码")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 调试API测试失败: {e}")

if __name__ == "__main__":
    test_debug_verify()
