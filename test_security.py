#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全框架测试脚本

用于测试安全框架的各项功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from security_client import SecurityClient
    print("✅ 安全框架模块导入成功")
except ImportError as e:
    print(f"❌ 安全框架模块导入失败: {e}")
    sys.exit(1)

def test_device_id():
    """测试设备ID生成"""
    print("\n🔍 测试设备ID生成...")
    
    client = SecurityClient(
        app_id="test_app",
        license_key="test_key",
        enable_anti_debug=False  # 禁用反调试
    )
    
    device_id = client.get_device_id()
    print(f"设备ID: {device_id}")
    print(f"设备ID长度: {len(device_id)}")
    
    # 测试设备ID的一致性
    client2 = SecurityClient(
        app_id="test_app",
        license_key="test_key",
        enable_anti_debug=False
    )
    device_id2 = client2.get_device_id()
    
    if device_id == device_id2:
        print("✅ 设备ID生成一致性测试通过")
    else:
        print("❌ 设备ID生成一致性测试失败")

def test_anti_debug():
    """测试反调试功能"""
    print("\n🛡️ 测试反调试功能...")
    
    client = SecurityClient(
        app_id="test_app",
        license_key="test_key",
        enable_anti_debug=True  # 启用反调试
    )
    
    # 手动调用反调试检测
    is_debug = client._detect_debugger()
    print(f"反调试检测结果: {'检测到调试环境' if is_debug else '未检测到调试环境'}")

def test_license_verification():
    """测试授权验证"""
    print("\n🔐 测试授权验证...")
    
    # 使用测试授权码
    client = SecurityClient(
        app_id="haoke_downloader",
        license_key="test_license_key_for_development_only_2024",
        api_base="https://haoke.kechengmao.top/api",
        enable_anti_debug=False  # 禁用反调试以便测试
    )
    
    print("正在连接服务器验证授权...")
    success, message, data = client.verify_license()
    
    print(f"验证结果: {'成功' if success else '失败'}")
    print(f"返回消息: {message}")
    
    if data:
        print("授权数据:")
        for key, value in data.items():
            print(f"  {key}: {value}")
    
    return success

def test_invalid_license():
    """测试无效授权码"""
    print("\n❌ 测试无效授权码...")
    
    client = SecurityClient(
        app_id="haoke_downloader",
        license_key="invalid_license_key_12345",
        api_base="https://haoke.kechengmao.top/api",
        enable_anti_debug=False
    )
    
    success, message, data = client.verify_license()
    
    print(f"验证结果: {'成功' if success else '失败'} (应该失败)")
    print(f"返回消息: {message}")

def test_network_error():
    """测试网络错误处理"""
    print("\n🌐 测试网络错误处理...")
    
    client = SecurityClient(
        app_id="haoke_downloader",
        license_key="test_license_key_for_development_only_2024",
        api_base="http://invalid-domain-12345.com/api",  # 无效域名
        enable_anti_debug=False
    )
    
    success, message, data = client.verify_license()
    
    print(f"验证结果: {'成功' if success else '失败'} (应该失败)")
    print(f"返回消息: {message}")

def main():
    """主测试函数"""
    print("🧪 安全框架功能测试")
    print("=" * 50)
    
    # 基础功能测试
    test_device_id()
    test_anti_debug()
    
    # 网络功能测试
    print("\n🌐 网络功能测试")
    print("-" * 30)
    
    # 测试有效授权
    valid_result = test_license_verification()
    
    # 测试无效授权
    test_invalid_license()
    
    # 测试网络错误
    test_network_error()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 50)
    
    if valid_result:
        print("✅ 安全框架基本功能正常")
        print("✅ 服务器连接正常")
        print("✅ 授权验证功能正常")
        print("\n🎉 可以正常使用好课在线下载器")
        print("\n💡 如需禁用反调试检测，请在config.json中设置:")
        print('   "enable_anti_debug": false')
    else:
        print("❌ 授权验证失败")
        print("💡 可能的原因:")
        print("   1. 服务器未部署或无法访问")
        print("   2. 网络连接问题")
        print("   3. 授权码已过期或无效")
        print("\n🔧 解决方案:")
        print("   1. 检查网络连接")
        print("   2. 确认服务器状态")
        print("   3. 联系管理员获取有效授权码")

if __name__ == "__main__":
    main()
