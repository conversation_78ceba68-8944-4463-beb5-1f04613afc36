# 好课在线自动化下载器 - 多账号增强版

基于配置文件的自动化课程下载工具，支持多账号管理、假空文件跳过机制，专注系统课下载。

## 🚀 主要特性

- ✅ **多账号支持**: 一个配置文件管理多个账号，每个账号独立课程列表
- ✅ **智能账号选择**: 支持手动选择账号或依次执行所有账号
- ✅ **自动登录**: 基于配置文件自动登录，无需手动输入
- ✅ **批量下载**: 支持配置多个课程ID进行批量下载
- ✅ **智能跳过**: 使用占位文件机制，避免重复下载
- ✅ **系统课专用**: 专门针对系统课程优化
- ✅ **断点续传**: 支持中断后继续下载
- ✅ **中文界面**: 全中文提示，操作简单
- ✅ **进度显示**: 实时显示下载进度
- ✅ **资料下载**: 自动下载随堂资料
- 🆕 **递归文件夹处理**: 完美支持多层嵌套文件夹下载（最多5层）
- 🆕 **老师姓名显示**: 文件夹自动显示"课程名称-老师姓名"格式
- 🆕 **多API容错**: 7种API尝试机制，大幅提升下载成功率

## 📁 文件夹结构

```
auto_haoke_downloader/
├── auto_downloader.py    # 主程序 (多账号版)
├── login.py              # 登录模块
├── config.json           # 多账号配置文件
├── requirements.txt      # 依赖包列表
├── README.md            # 使用说明
├── 打包脚本.bat          # 自动打包脚本
├── downloads/           # 真实文件下载目录
│   ├── 好课在线-主账号/   # 按账号分类的文件夹
│   │   ├── 【2025秋】高考数学目标清北班（一轮）-张老师/    # 课程名称-老师姓名
│   │   │   ├── 01_第1讲 【启航一轮】导数中切线问题经典考法/
│   │   │   │   ├── 01.第1讲 【启航一轮】导数中切线问题经典考法.mp4
│   │   │   │   └── 01.随堂资料.pdf
│   │   │   └── 课程资料/
│   │   │       ├── 高考一轮闯关训练/
│   │   │       │   ├── 01-01.【一轮闯关训练】-导数.pdf
│   │   │       │   └── 01-02.【一轮闯关训练】-三角.pdf
│   │   │       └── 02.五年高考真题题型分类汇编.pdf
│   │   └── 【2025秋】高考物理目标双一流班（一轮）-李老师/
│   │       └── 课程资料/
│   │           └── 25新高三/
│   │               ├── 选修五年高考分类汇编/
│   │               │   ├── 01-01-01.专题19几何光学.pdf
│   │               │   └── 01-01-02.专题21机械振动和机械波.pdf
│   │               └── 选修满分宝/
│   │                   ├── 选修知识清单/
│   │                   │   ├── 01-04-01-01.【知识清单】热学.pdf
│   │                   │   └── 01-04-01-02.【知识清单】原子物理.pdf
│   │                   └── 选修实验汇编/
│   │                       ├── 01-04-02-01.机械振动和光学实验手册.pdf
│   │                       └── 01-04-02-02.热学实验手册.pdf
│   └── 好课在线-备用账号/
│       └── 高一数学培训班（24春·尖端班）-王老师/
├── placeholders/        # 占位文件目录
└── cookies/            # Cookie存储目录
    ├── haoke_cookies_123456789.txt    # 账号1的cookie
    └── haoke_cookies_987654321.txt    # 账号2的cookie
```

## 🛠️ 安装依赖

```bash
pip install -r requirements.txt
```

## ⚙️ 配置设置

### 1. 多账号配置 config.json

```json
{
    "账号列表": [
        {
            "账号名称": "",
            "用户名": "",
            "密码": "",
            "课程ID列表": [
                "",
                ""
            ],
            "启用": true,
            "备注": ""
        },
        {
            "账号名称": "",
            "用户名": "",
            "密码": "",
            "课程ID列表": [
                "",
                ""
            ],
            "启用": true,
            "备注": "备用账号"
        }
    ],
    "执行模式": {
        "模式": "选择账号",
        "说明": "可选值: 选择账号(手动选择) | 依次执行(按顺序执行所有启用账号)"
    },
    "课程配置": {
        "只下载系统课": true,
        "课程说明": "每个账号可以有独立的课程ID列表"
    },
    "下载设置": {
        "下载内容选择": 0,
        "下载内容说明": "0=全部内容(视频+独立课程资料) | 1=仅视频(包含随堂资料) | 2=仅课程资料",
        "下载后创建占位文件": true,
        "自动下载全部章节": true,
        "下载视频和资料": true,
        "最大并发下载数": 16,
        "下载重试次数": 3
    },
    "文件夹设置": {
        "真实文件目录": "downloads",
        "占位文件目录": "placeholders",
        "Cookie文件目录": "cookies"
    }
}
```

### 2. 配置说明

#### 账号配置
- **账号名称**: 给账号起个名字，方便识别
- **用户名**: 手机号
- **密码**: 登录密码
- **课程ID列表**: 该账号要下载的课程ID数组
- **启用**: `true`启用账号，`false`禁用账号
- **备注**: 可选，账号说明

#### 执行模式
- **选择账号**: 程序启动后手动选择要使用的账号
- **依次执行**: 自动依次执行所有启用的账号

#### 下载设置
- **下载内容选择**: 控制下载什么内容
  - `0`: 全部内容（下载视频+随堂资料+独立课程资料）✨ **推荐设置**
  - `1`: 仅视频（下载视频+包含的随堂资料，默认值）
  - `2`: 仅课程资料（只下载课程级别的独立资料，不下载视频）
- **下载后创建占位文件**: 控制是否创建占位文件机制
- **下载视频和资料**: 是否同时下载随堂资料（对选项0和1有效）
- **最大并发下载数**: 同时下载的分段数量
- **下载重试次数**: 下载失败时的重试次数

## 🚀 运行程序

### 源代码运行
```bash
python auto_downloader.py
```

### 打包运行
```bash
# 自动打包（需要先安装依赖）
打包脚本.bat

# 运行打包后的程序
dist/好课在线自动化下载器.exe
```

## 💡 多账号工作原理

### 账号管理
1. **独立认证**: 每个账号维护独立的cookie文件
2. **分类下载**: 不同账号的文件下载到不同文件夹
3. **课程隔离**: 每个账号有独立的课程ID列表
4. **状态管理**: 可以单独启用/禁用某个账号

### 执行流程
1. **启动程序**: 读取配置文件，检查启用的账号
2. **选择模式**: 
   - 选择账号模式：显示账号列表供选择
   - 依次执行模式：自动处理所有启用账号
3. **账号处理**: 登录 → 下载课程 → 下一个账号
4. **文件管理**: 按账号名称分类存储

### 占位文件机制

```
downloads/好课在线-主账号/数学系统课/01_开学第一课.mp4     ← 真实文件（可删除）
placeholders/好课在线-主账号/数学系统课/01_开学第一课.mp4  ← 空标记文件（保留）
```

## 🌟 核心技术亮点

### 1. 递归文件夹处理 🔄
完美支持课程资料的多层嵌套文件夹结构：

**支持的文件夹层级**：
- **一层**: `课程资料/文件.pdf`
- **二层**: `课程资料/高考一轮闯关训练/文件.pdf`
- **三层**: `课程资料/25新高三/选修五年高考分类汇编/文件.pdf`
- **四层**: `课程资料/25新高三/选修满分宝/选修知识清单/文件.pdf`
- **五层**: `课程资料/25新高三/力学五年高考真题分类汇编/力学/文件.pdf`

**智能编号系统**：
```
01.直接文件.pdf                    # 一层文件
01-01.文件夹内文件.pdf              # 二层文件  
01-01-01.子文件夹内文件.pdf         # 三层文件
01-04-01-01.深层嵌套文件.pdf        # 四层文件
01-05-03-01.最深层文件.pdf          # 五层文件
```

### 2. 多API容错机制 🛡️
实现7种不同API尝试策略，解决"无法获取直播间ID"问题：

**API尝试顺序**：
1. **系统课标准API** (c3域名)
2. **AI课堂API** (c4域名) - 支持最新课程格式
3. **专项课API** (c4域名) - 兼容专项课程
4. **简化参数API** - 降级尝试
5. **课程ID替代API** - 无直播间ID时的智能替代
6. **备用域名API** - 域名切换重试
7. **最后尝试API** - 最小参数集尝试

**现在支持的特殊章节**：
- ✅ "视频·学习规划课"
- ✅ "视频【第X讲】题型精练"  
- ✅ "期中复习"、"期末复习"
- ✅ AI课堂格式课程
- ✅ 无直播间ID的章节

### 3. 智能老师信息获取 👨‍🏫
采用与源代码完全一致的API调用方式：

**实现方式**：
- 使用POST请求获取完整课程列表
- 匹配课程ID获取老师信息
- 自动组合"课程名称-老师姓名"格式
- 失败时优雅降级到基础课程名称

**显示效果**：
```
📚 课程名称: 【2025秋】高考数学目标清北班（一轮）-张老师
📚 课程名称: 高一数学培训班（24春·尖端班）-王老师
```

### 4. 智能效率优化 ⚡
先检查后请求的高效逻辑：

**优化前**（低效）：
```
处理章节 → API获取视频地址 → 创建文件夹 → 检查占位文件 → 发现已存在 → 跳过
```

**优化后**（高效）：
```
处理章节 → 检查占位文件 → 已存在直接跳过 ✅
           ↓
         不存在 → API获取视频地址 → 下载
```

**实际效果**：
- 已下载章节：0个网络请求
- 需要下载章节：必要的网络请求
- 大幅减少API调用，提升运行速度

## 📖 使用流程

### 初次使用
1. **配置账号**: 在 `config.json` 中添加多个账号信息
2. **设置课程**: 为每个账号配置要下载的课程ID列表
3. **选择下载模式**: 设置 `下载内容选择` 参数
   - `0`: 全部内容（✨ **强烈推荐**，包含视频和独立课程资料）
   - `1`: 仅视频（默认，视频+随堂资料）
   - `2`: 仅课程资料（只下载课程级别的文档资料）
4. **启用账号**: 将要使用的账号的 `启用` 设置为 `true`
5. **运行程序**: 执行 `python auto_downloader.py` 或使用打包后的exe

### 交互式使用
```
📋 可用账号列表:
==================================================
1. 主账号 ()
   课程数量: 2 个
   备注: 主要学习账号

2. 备用账号 ()
   课程数量: 2 个
   备注: 备用账号

0. 依次执行所有账号

请选择要使用的账号 (输入序号): 
```

### 批量执行
将执行模式设置为 `"依次执行"`，程序会自动处理所有启用的账号。

## ⚠️ 注意事项

- 程序只支持系统课下载
- 每个账号维护独立的cookie文件，避免冲突
- 不同账号的文件下载到不同文件夹
- 中断下载后可以重新运行，会自动跳过已下载内容
- 占位文件很小，建议长期保留避免重复下载
- 递归文件夹处理最大深度为5层，防止无限嵌套

## 🔧 高级配置

### 账号管理技巧

1. **临时禁用账号**: 将 `启用` 设置为 `false`
2. **课程分配**: 不同账号下载不同类型的课程
3. **备注管理**: 使用备注字段记录账号用途
4. **批量操作**: 使用依次执行模式处理多个账号

### 文件管理

每个账号的文件存储在独立文件夹中：
```
downloads/
├── 好课在线-主账号/
│   ├── 【2025秋】高考数学目标清北班（一轮）-张老师/
│   └── 【2025秋】高考物理目标双一流班（一轮）-李老师/
└── 好课在线-备用账号/
    ├── 高一数学培训班（24春·尖端班）-王老师/
    └── 高二语文系统课（春季班）-刘老师/
```

## 🐛 常见问题

**Q: 如何添加新账号？**
A: 在 `config.json` 的 `账号列表` 中添加新的账号对象，设置 `启用: true`。

**Q: 账号登录失败怎么办？**
A: 检查对应账号的用户名和密码，确保网络连接正常。每个账号的错误会单独处理。

**Q: 如何让某个账号下载不同的课程？**
A: 修改对应账号的 `课程ID列表` 数组。

**Q: 多层嵌套文件夹下载失败怎么办？**
A: 程序支持最多5层嵌套，如果超过此限制会自动跳过并提示。

**Q: 老师姓名没有显示怎么办？**
A: 程序会自动尝试获取老师信息，如果失败会使用基础课程名称，不影响下载功能。

**Q: 如何清理某个账号的下载历史？**
A: 删除 `placeholders/好课在线-账号名称/` 目录下对应的占位文件。

**Q: Cookie文件冲突怎么办？**
A: 每个账号使用独立的cookie文件，不会冲突。文件命名格式：`haoke_cookies_手机号.txt`

## 📝 版本更新

### v2.2 - 完美增强版 🔥🆕
- 🎯 **递归文件夹处理**: 完美支持5层嵌套文件夹下载，智能编号系统
- 👨‍🏫 **老师姓名显示**: 使用与源代码一致的API获取老师信息，显示"课程名称-老师姓名"
- 🛡️ **多API容错**: 7种API尝试机制，解决"无法获取直播间ID"问题
- ⚡ **效率优化**: 先检查占位文件再发送网络请求，大幅提升运行效率
- 🔧 **API修复**: 修复所有课程资料API调用问题，支持嵌套文件夹结构
- 📦 **打包支持**: 新增自动打包脚本，方便分发exe程序
- ✅ **全面测试**: 所有功能经过充分测试，确保稳定运行

### v2.1 - 多API增强修复版
- ✅ **重大修复**: 解决"无法获取直播间ID，跳过章节"的严重问题
- ✅ **多API支持**: 新增7种不同API尝试机制，大幅提升下载成功率
- ✅ **智能章节处理**: 支持"视频·学习规划课"、"视频【第X讲】题型精练"等特殊章节
- ✅ **AI课堂API**: 支持AI课堂和传统API双重保障
- ✅ **专项课支持**: 添加c4域名API支持，兼容专项课下载
- ✅ **容错机制**: 单个API失败时自动尝试其他API，不再轻易跳过章节
- ✅ **智能文件夹管理**: 优化文件夹创建逻辑，只有下载成功才创建章节文件夹，避免空文件夹
- 🚀 **效率优化**: 先检查占位文件再发送网络请求，避免不必要的API调用，大幅提升运行效率
- 📝 **老师信息显示**: 课程文件夹现在显示"课程名称-老师姓名"格式，与原版保持一致
- 🎛️ **下载内容选择**: 新增配置项控制下载内容，支持全部/仅视频/仅资料三种模式
- 🔧 **API修复**: 修复课程资料API调用错误，解决模式0下"独立课程资料下载失败"问题
- 🔧 **多账号优化**: 优化API调用顺序，修复多账号环境下课程信息获取失败问题

### v2.0 - 多账号增强版
- ✅ 新增多账号配置支持
- ✅ 账号选择和批量执行模式
- ✅ 独立的cookie和文件管理
- ✅ 按账号分类的文件夹结构
- ✅ 改进的用户界面和提示信息

### v1.0 - 基础版
- 基于原版好课在线下载器改造
- 专注于自动化和批量下载
- 优化了文件管理和跳过机制
- 支持配置文件驱动，无需手动交互

## 🚨 重要更新说明

### 关于递归文件夹处理

**支持的嵌套结构**:
程序现在完美支持课程资料的多层嵌套文件夹，实际测试案例：

```
📁 25新高三/
├── 📁 选修五年高考分类汇编/
│   ├── 01-01-01.专题19几何光学.pdf
│   └── 01-01-02.专题21机械振动和机械波.pdf
├── 📁 电磁学五年高考分类汇编/
│   ├── 01-02-01.专题14恒定电流与电学实验.pdf
│   └── 01-02-02.专题12静电场.pdf
├── 📁 选修满分宝/
│   ├── 📁 选修知识清单/
│   │   ├── 01-04-01-01.【知识清单】热学.pdf
│   │   └── 01-04-01-02.【知识清单】原子物理.pdf
│   ├── 📁 选修实验汇编/
│   │   ├── 01-04-02-01.机械振动和光学实验手册.pdf
│   │   └── 01-04-02-02.热学实验手册.pdf
│   └── 📁 选修满分模板/
│       ├── 01-04-03-01.气缸-单缸（固定缸）.pdf
│       └── 01-04-03-02.光路综合分析-球（柱）形介质.pdf
└── 📁 力学五年高考真题分类汇编/
    └── 📁 力学/
        ├── 01-05-03-01.专题01匀变速直线运动.pdf
        ├── 01-05-03-02.专题04抛体运动.pdf
        └── 01-05-03-03.专题06万有引力与航天.pdf
```

### 关于"无法获取直播间ID"问题的修复

**问题描述**: 
之前版本遇到某些特殊章节（如"视频·学习规划课"、"视频【第X讲】题型精练"）时，会提示"⚠️ 无法获取直播间ID，跳过章节"，导致这些章节无法下载。

**解决方案**:
1. **多API机制**: 实现了7种不同的API尝试策略
2. **智能替代**: 当无直播间ID时，使用课程ID作为替代参数
3. **域名切换**: 自动尝试c3和c4两个不同域名的API
4. **AI课堂支持**: 新增AI课堂专用API支持
5. **容错处理**: 单个API失败时继续尝试其他API，不再直接跳过

**现在支持的API类型**:
- 系统课标准API (c3域名)
- AI课堂API (c4域名) 
- 专项课API (c4域名)
- 简化参数API
- 课程ID替代API

### API尝试顺序示例

```
🔍 尝试API 1/7: 系统课标准API
⚠️ API 1 返回错误: 直播间不存在
🔍 尝试API 2/7: AI课堂API  
✅ 通过API 2 成功获取视频地址
``` 

---

## 📞 技术支持

如果您在使用过程中遇到问题，可以：
1. 检查配置文件格式是否正确
2. 确认网络连接正常
3. 查看程序运行日志获取错误详情
4. 重新获取cookie信息

**程序特色**：无需复杂操作，配置一次即可长期使用！

## 🚀 未来优化方向

### 当前状态评估
程序已经非常完善，核心功能稳定可靠。以下是一些可考虑的增强方向：

### 1. 性能优化方向 ⚡
- **多账号并发下载**: 支持同时使用多个账号并行下载，提升整体效率
- **智能重试策略**: 根据错误类型采用不同的重试间隔和次数
- **断点续传增强**: 支持大文件的真正断点续传，而非仅跳过机制
- **课程信息缓存**: 缓存课程目录信息，减少重复API调用

### 2. 用户体验优化 🌟
- **实时进度监控**: 显示下载速度、剩余时间、网络状态等实时信息
- **GUI可选界面**: 提供图形化操作界面，降低使用门槛
- **下载队列管理**: 可视化下载队列，支持暂停/恢复/优先级调整
- **自动更新检查**: 程序启动时自动检查是否有新版本

### 3. 稳定性增强 🛡️
- **网络异常处理**: 网络断开时自动暂停，恢复后继续下载
- **存储空间检查**: 下载前检查磁盘剩余空间，避免下载失败
- **Cookie自动刷新**: 检测到cookie失效时自动重新登录
- **错误分析报告**: 生成详细的错误统计和建议修复方案

### 4. 功能扩展方向 📈
- **批量账号管理**: 支持从Excel/CSV文件批量导入账号信息
- **下载计划任务**: 支持定时下载和计划任务设置
- **多平台支持**: 扩展支持其他在线教育平台
- **云同步配置**: 支持配置文件云端同步，多设备共享

### 5. 高级特性 🔧
- **API接口模式**: 提供HTTP API接口，支持远程调用
- **插件系统**: 支持第三方插件扩展功能
- **分布式下载**: 支持多台电脑协同下载大型课程
- **智能分类**: 根据课程内容自动分类整理文件

### 🎯 优先级建议

**短期优化**（实用性高，实现难度低）：
1. 实时进度监控和下载速度显示
2. 智能重试策略和网络异常处理
3. 课程信息缓存机制
4. 存储空间检查

**中期优化**（功能增强，提升体验）：
1. 多账号并发下载
2. 可选GUI界面
3. 自动更新检查
4. Cookie自动刷新

**长期优化**（高级特性，扩展能力）：
1. 批量账号管理
2. 下载计划任务
3. 多平台支持
4. API接口模式

### 💡 当前建议
程序现阶段已经完全满足核心下载需求，建议：
- **优先使用**: 当前版本功能完善，稳定可靠
- **按需优化**: 根据实际使用中遇到的具体问题进行针对性优化
- **保持简洁**: 避免过度复杂化，保持程序的易用性

**结论**: 当前v2.2版本已经是一个功能完备、性能优异的自动化下载工具，足以应对绝大多数使用场景。上述优化方向可作为未来版本的参考，但不是必需的。 