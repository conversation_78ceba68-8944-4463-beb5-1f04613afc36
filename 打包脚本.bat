@echo off
:: 设置UTF-8编码
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion
echo ===============================================
echo 好课在线自动化下载器 - 自动打包脚本
echo ===============================================
echo.
echo [安全提示] 本脚本采用覆盖式更新，你的下载文件完全安全
echo.
echo 按任意键开始打包...
pause >nul
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python
    echo [提示] 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [成功] Python环境检测成功
echo.

:: 检查是否存在虚拟环境
if exist "venv" (
    echo [信息] 检测到虚拟环境，正在激活...
    call venv\Scripts\activate.bat
    echo [成功] 虚拟环境激活成功
) else (
    echo [信息] 未检测到虚拟环境，使用全局Python环境
)
echo.

:: 安装/更新必要依赖
echo [进行中] 正在安装/更新依赖包...
pip install --upgrade pip
pip install -r requirements.txt
pip install pyinstaller

echo.
echo [成功] 依赖包安装完成

:: 验证 pyinstaller 是否正确安装
echo [验证] 检查 pyinstaller 安装状态...
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo [警告] pyinstaller 未正确安装，尝试重新安装...
    pip uninstall pyinstaller -y >nul 2>&1
    pip install pyinstaller
    
    :: 再次验证
    python -m PyInstaller --version >nul 2>&1
    if errorlevel 1 (
        echo [错误] pyinstaller 安装失败，请手动安装：
        echo   pip install pyinstaller
        pause
        exit /b 1
    )
)
echo [成功] pyinstaller 验证通过
echo.

:: 清理之前的打包文件
echo [进行中] 清理构建缓存...

:: 只清理构建缓存，保留所有用户文件
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "*.spec" del /q "*.spec" >nul 2>&1

echo [成功] 构建缓存清理完成
echo [提示] 新程序将直接覆盖旧版本，你的下载文件完全安全
echo.

:: 开始打包
echo [进行中] 开始打包程序...
echo [配置] 打包配置信息：
echo    - 主程序：auto_downloader.py
echo    - 输出名称：好课在线自动化下载器
echo    - 包含文件：config.json, requirements.txt
echo    - 图标：无（使用默认）
echo    - 模式：单文件模式
echo.

python -m PyInstaller ^
    --onefile ^
    --console ^
    --name "好课在线自动化下载器" ^
    --add-data "config.json;." ^
    --add-data "requirements.txt;." ^
    --add-data "README.md;." ^
    --hidden-import "requests" ^
    --hidden-import "m3u8" ^
    --hidden-import "tqdm" ^
    --hidden-import "urllib3" ^
    --hidden-import "login" ^
    --clean ^
    auto_downloader.py

if errorlevel 1 (
    echo.
    echo [错误] 打包失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo [成功] 打包成功！
echo.

:: 检查输出文件
if exist "dist\好课在线自动化下载器.exe" (
    echo [输出] 文件信息：
    echo    文件路径：%cd%\dist\好课在线自动化下载器.exe
    for %%I in ("dist\好课在线自动化下载器.exe") do echo    文件大小：%%~zI 字节
    echo.
    
    :: 复制必要的配置文件到dist目录
    echo [进行中] 复制配置文件到输出目录...
    copy "README.md" "dist\" >nul 2>&1
    copy "启动下载器.bat" "dist\" >nul 2>nul
    echo [成功] 配置文件复制完成
    echo.
    
    echo [完成] 打包完成！
    echo.
    echo [输出目录] %cd%\dist\
    echo [可执行文件] 好课在线自动化下载器.exe
    echo [配置文件] config.json
    echo [说明文档] README.md
    echo.
    echo [使用说明]
    echo    1. 将 dist 文件夹复制到目标电脑
    echo    2. 编辑 config.json 配置账号信息
    echo    3. 双击运行 好课在线自动化下载器.exe
    echo.
    echo [测试] 是否现在测试运行？(Y/N)
    set /p test_run=
    if /i "%test_run%"=="Y" (
        echo [启动] 启动测试运行...
        cd dist
        "好课在线自动化下载器.exe"
        cd ..
    )
    
) else (
    echo [错误] 打包失败：未找到输出文件
    pause
    exit /b 1
)

echo.
echo [完成] 打包脚本执行完成！
pause 