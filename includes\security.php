<?php
/**
 * 通用安全框架 - 安全类库
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 防止直接访问
if (!defined('SECURITY_FRAMEWORK')) {
    die('Direct access denied');
}

/**
 * 安全工具类
 * 提供加密、签名、验证等安全功能
 */
class Security {
    
    /**
     * 生成API请求签名
     * 
     * @param array $params 请求参数
     * @param string $secret 应用密钥
     * @param int $timestamp 时间戳
     * @return string 签名字符串
     */
    public static function generateSignature($params, $secret, $timestamp) {
        // 移除签名字段
        unset($params['signature']);
        
        // 参数排序
        ksort($params);
        
        // 构建查询字符串
        $queryString = http_build_query($params);
        
        // 添加时间戳和密钥
        $signString = $queryString . '&timestamp=' . $timestamp . '&secret=' . $secret;
        
        // 生成HMAC-SHA256签名
        return hash_hmac('sha256', $signString, $secret);
    }
    
    /**
     * 验证API请求签名
     * 
     * @param array $params 请求参数
     * @param string $secret 应用密钥
     * @param int $timestamp 时间戳
     * @param string $signature 待验证的签名
     * @return bool 验证结果
     */
    public static function verifySignature($params, $secret, $timestamp, $signature) {
        // 检查时间戳(防重放攻击)
        $currentTime = time();
        $timeDiff = abs($currentTime - $timestamp);
        
        if ($timeDiff > VERIFY_TIMEOUT) {
            writeLog('WARNING', 'Signature verification failed: timestamp expired', [
                'current_time' => $currentTime,
                'request_timestamp' => $timestamp,
                'time_diff' => $timeDiff,
                'max_allowed' => VERIFY_TIMEOUT
            ]);
            return false;
        }
        
        // 生成期望的签名
        $expectedSignature = self::generateSignature($params, $secret, $timestamp);
        
        // 使用时间安全的比较
        $result = hash_equals($expectedSignature, $signature);
        
        if (!$result) {
            writeLog('WARNING', 'Signature verification failed: signature mismatch', [
                'expected' => $expectedSignature,
                'received' => $signature,
                'params' => $params
            ]);
        }
        
        return $result;
    }
    
    /**
     * 加密数据
     * 
     * @param string $data 待加密数据
     * @param string $key 加密密钥
     * @return string 加密后的数据(Base64编码)
     */
    public static function encrypt($data, $key = null) {
        $key = $key ?: ENCRYPTION_KEY;
        
        // 生成随机IV
        $iv = random_bytes(16);
        
        // AES-256-CBC加密
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        
        if ($encrypted === false) {
            throw new Exception('数据加密失败');
        }
        
        // 返回IV+加密数据的Base64编码
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密数据
     * 
     * @param string $encryptedData 加密的数据(Base64编码)
     * @param string $key 解密密钥
     * @return string 解密后的数据
     */
    public static function decrypt($encryptedData, $key = null) {
        $key = $key ?: ENCRYPTION_KEY;
        
        // Base64解码
        $data = base64_decode($encryptedData);
        
        if ($data === false || strlen($data) < 16) {
            throw new Exception('无效的加密数据');
        }
        
        // 提取IV和加密数据
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        // AES-256-CBC解密
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        
        if ($decrypted === false) {
            throw new Exception('数据解密失败');
        }
        
        return $decrypted;
    }
    
    /**
     * 生成随机字符串
     * 
     * @param int $length 字符串长度
     * @param string $chars 字符集
     * @return string 随机字符串
     */
    public static function generateRandomString($length = 32, $chars = null) {
        if ($chars === null) {
            // 默认字符集：字母数字
            $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        }
        
        $result = '';
        $charsLength = strlen($chars);
        
        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[random_int(0, $charsLength - 1)];
        }
        
        return $result;
    }
    
    /**
     * 生成十六进制随机字符串
     * 
     * @param int $length 字符串长度(必须是偶数)
     * @return string 十六进制随机字符串
     */
    public static function generateHexString($length = 32) {
        if ($length % 2 !== 0) {
            throw new InvalidArgumentException('Length must be even');
        }
        
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * 哈希密码
     * 
     * @param string $password 明文密码
     * @return string 哈希后的密码
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64MB
            'time_cost' => 4,       // 4次迭代
            'threads' => 3          // 3个线程
        ]);
    }
    
    /**
     * 验证密码
     * 
     * @param string $password 明文密码
     * @param string $hash 哈希密码
     * @return bool 验证结果
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * 清理输入数据
     * 
     * @param mixed $input 输入数据
     * @return mixed 清理后的数据
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        if (is_string($input)) {
            // 移除首尾空白
            $input = trim($input);
            
            // HTML实体编码
            $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            
            return $input;
        }
        
        return $input;
    }
    
    /**
     * 验证IP地址
     * 
     * @param string $ip IP地址
     * @return bool 验证结果
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * 验证邮箱地址
     * 
     * @param string $email 邮箱地址
     * @return bool 验证结果
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * 验证URL
     * 
     * @param string $url URL地址
     * @return bool 验证结果
     */
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * 生成设备指纹验证码
     * 
     * @param array $deviceInfo 设备信息
     * @return string 设备指纹
     */
    public static function generateDeviceFingerprint($deviceInfo) {
        // 确保数据一致性
        $data = json_encode($deviceInfo, JSON_SORT_KEYS | JSON_UNESCAPED_UNICODE);
        
        // 生成SHA256哈希
        return hash('sha256', $data);
    }
    
    /**
     * 生成CSRF令牌
     * 
     * @return string CSRF令牌
     */
    public static function generateCSRFToken() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = self::generateHexString(32);
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_time'] = time();
        
        return $token;
    }
    
    /**
     * 验证CSRF令牌
     * 
     * @param string $token 待验证的令牌
     * @return bool 验证结果
     */
    public static function verifyCSRFToken($token) {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_time'])) {
            return false;
        }
        
        // 检查令牌是否过期(30分钟)
        if (time() - $_SESSION['csrf_time'] > 1800) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_time']);
            return false;
        }
        
        // 验证令牌
        $result = hash_equals($_SESSION['csrf_token'], $token);
        
        if ($result) {
            // 验证成功后删除令牌(一次性使用)
            unset($_SESSION['csrf_token'], $_SESSION['csrf_time']);
        }
        
        return $result;
    }
    
    /**
     * 检查请求频率限制
     * 
     * @param string $identifier 标识符(IP、用户ID等)
     * @param int $maxRequests 最大请求数
     * @param int $timeWindow 时间窗口(秒)
     * @return bool 是否允许请求
     */
    public static function checkRateLimit($identifier, $maxRequests = null, $timeWindow = null) {
        $maxRequests = $maxRequests ?: RATE_LIMIT_REQUESTS;
        $timeWindow = $timeWindow ?: RATE_LIMIT_WINDOW;
        
        $key = 'rate_limit_' . md5($identifier);
        $cacheFile = LOGS_PATH . '/' . $key . '.cache';
        
        $now = time();
        $requests = [];
        
        // 读取现有记录
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $requests = json_decode($data, true) ?: [];
        }
        
        // 清理过期记录
        $requests = array_filter($requests, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // 检查是否超出限制
        if (count($requests) >= $maxRequests) {
            writeLog('WARNING', 'Rate limit exceeded', [
                'identifier' => $identifier,
                'requests_count' => count($requests),
                'max_requests' => $maxRequests,
                'time_window' => $timeWindow
            ]);
            return false;
        }
        
        // 记录当前请求
        $requests[] = $now;
        
        // 保存到缓存文件
        file_put_contents($cacheFile, json_encode($requests));
        
        return true;
    }
}

?>
