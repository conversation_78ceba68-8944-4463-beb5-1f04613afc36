<?php
/**
 * 通用安全框架 - 日志记录类
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 防止直接访问
if (!defined('SECURITY_FRAMEWORK')) {
    die('Direct access denied');
}

/**
 * 日志记录类
 * 提供结构化日志记录功能
 */
class Logger {
    
    // 日志级别常量
    const EMERGENCY = 'EMERGENCY';
    const ALERT = 'ALERT';
    const CRITICAL = 'CRITICAL';
    const ERROR = 'ERROR';
    const WARNING = 'WARNING';
    const NOTICE = 'NOTICE';
    const INFO = 'INFO';
    const DEBUG = 'DEBUG';
    
    // 日志级别权重
    private static $levels = [
        self::EMERGENCY => 800,
        self::ALERT => 700,
        self::CRITICAL => 600,
        self::ERROR => 500,
        self::WARNING => 400,
        self::NOTICE => 300,
        self::INFO => 200,
        self::DEBUG => 100
    ];
    
    private $logPath;
    private $maxFileSize;
    private $maxFiles;
    
    public function __construct($logPath = null, $maxFileSize = 10485760, $maxFiles = 10) {
        $this->logPath = $logPath ?: LOGS_PATH . '/security.log';
        $this->maxFileSize = $maxFileSize; // 10MB
        $this->maxFiles = $maxFiles;
        
        // 确保日志目录存在
        $logDir = dirname($this->logPath);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * 记录日志
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    public function log($level, $message, $context = []) {
        // 检查日志级别
        if (!$this->shouldLog($level)) {
            return;
        }
        
        // 构建日志记录
        $logRecord = $this->formatLogRecord($level, $message, $context);
        
        // 写入日志文件
        $this->writeToFile($logRecord);
        
        // 检查文件大小并轮转
        $this->rotateIfNeeded();
    }
    
    /**
     * 检查是否应该记录此级别的日志
     * 
     * @param string $level 日志级别
     * @return bool
     */
    private function shouldLog($level) {
        $currentLevel = LOG_LEVEL;
        
        if (!isset(self::$levels[$level]) || !isset(self::$levels[$currentLevel])) {
            return true;
        }
        
        return self::$levels[$level] >= self::$levels[$currentLevel];
    }
    
    /**
     * 格式化日志记录
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return string 格式化的日志记录
     */
    private function formatLogRecord($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $microtime = sprintf('%06d', (microtime(true) - floor(microtime(true))) * 1000000);
        $pid = getmypid();
        $clientIP = getClientIP();
        
        // 基础日志信息
        $logData = [
            'timestamp' => $timestamp . '.' . $microtime,
            'level' => $level,
            'message' => $message,
            'pid' => $pid,
            'client_ip' => $clientIP,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ];
        
        // 添加上下文数据
        if (!empty($context)) {
            $logData['context'] = $context;
        }
        
        // 添加调用栈信息（仅在DEBUG级别）
        if ($level === self::DEBUG) {
            $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
            $logData['trace'] = array_slice($trace, 2); // 跳过logger自身的调用
        }
        
        // JSON格式输出
        return json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n";
    }
    
    /**
     * 写入日志文件
     * 
     * @param string $logRecord 日志记录
     */
    private function writeToFile($logRecord) {
        $attempts = 0;
        $maxAttempts = 3;
        
        while ($attempts < $maxAttempts) {
            $fp = fopen($this->logPath, 'a');
            
            if ($fp) {
                if (flock($fp, LOCK_EX)) {
                    fwrite($fp, $logRecord);
                    flock($fp, LOCK_UN);
                    fclose($fp);
                    return;
                }
                fclose($fp);
            }
            
            $attempts++;
            usleep(10000); // 等待10ms后重试
        }
        
        // 如果写入失败，使用系统错误日志
        error_log("Failed to write to log file: " . $this->logPath);
        error_log($logRecord);
    }
    
    /**
     * 检查并轮转日志文件
     */
    private function rotateIfNeeded() {
        if (!file_exists($this->logPath)) {
            return;
        }
        
        $fileSize = filesize($this->logPath);
        
        if ($fileSize >= $this->maxFileSize) {
            $this->rotateLogFiles();
        }
    }
    
    /**
     * 轮转日志文件
     */
    private function rotateLogFiles() {
        $pathInfo = pathinfo($this->logPath);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        $directory = $pathInfo['dirname'];
        
        // 删除最旧的日志文件
        $oldestFile = sprintf('%s/%s.%d.%s', $directory, $baseName, $this->maxFiles, $extension);
        if (file_exists($oldestFile)) {
            unlink($oldestFile);
        }
        
        // 重命名现有的日志文件
        for ($i = $this->maxFiles - 1; $i >= 1; $i--) {
            $oldFile = sprintf('%s/%s.%d.%s', $directory, $baseName, $i, $extension);
            $newFile = sprintf('%s/%s.%d.%s', $directory, $baseName, $i + 1, $extension);
            
            if (file_exists($oldFile)) {
                rename($oldFile, $newFile);
            }
        }
        
        // 重命名当前日志文件
        $firstRotatedFile = sprintf('%s/%s.1.%s', $directory, $baseName, $extension);
        rename($this->logPath, $firstRotatedFile);
        
        // 记录轮转事件
        $this->log(self::INFO, 'Log file rotated', [
            'old_file' => $this->logPath,
            'new_file' => $firstRotatedFile,
            'file_size' => $this->maxFileSize
        ]);
    }
    
    /**
     * 清理旧日志文件
     * 
     * @param int $days 保留天数
     */
    public function cleanOldLogs($days = 30) {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $logDir = dirname($this->logPath);
        $pathInfo = pathinfo($this->logPath);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        
        $pattern = sprintf('%s/%s.*.%s', $logDir, $baseName, $extension);
        $files = glob($pattern);
        
        $deletedCount = 0;
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                $deletedCount++;
            }
        }
        
        if ($deletedCount > 0) {
            $this->log(self::INFO, 'Old log files cleaned', [
                'deleted_count' => $deletedCount,
                'cutoff_days' => $days
            ]);
        }
    }
    
    /**
     * 获取日志统计信息
     * 
     * @return array 统计信息
     */
    public function getLogStats() {
        $stats = [
            'current_file_size' => file_exists($this->logPath) ? filesize($this->logPath) : 0,
            'max_file_size' => $this->maxFileSize,
            'max_files' => $this->maxFiles,
            'log_path' => $this->logPath
        ];
        
        // 统计轮转文件
        $logDir = dirname($this->logPath);
        $pathInfo = pathinfo($this->logPath);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        
        $pattern = sprintf('%s/%s.*.%s', $logDir, $baseName, $extension);
        $rotatedFiles = glob($pattern);
        
        $stats['rotated_files_count'] = count($rotatedFiles);
        $stats['total_log_size'] = $stats['current_file_size'];
        
        foreach ($rotatedFiles as $file) {
            $stats['total_log_size'] += filesize($file);
        }
        
        return $stats;
    }
    
    // 便捷方法
    public function emergency($message, $context = []) {
        $this->log(self::EMERGENCY, $message, $context);
    }
    
    public function alert($message, $context = []) {
        $this->log(self::ALERT, $message, $context);
    }
    
    public function critical($message, $context = []) {
        $this->log(self::CRITICAL, $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log(self::ERROR, $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log(self::WARNING, $message, $context);
    }
    
    public function notice($message, $context = []) {
        $this->log(self::NOTICE, $message, $context);
    }
    
    public function info($message, $context = []) {
        $this->log(self::INFO, $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log(self::DEBUG, $message, $context);
    }
}

/**
 * 全局日志函数（如果尚未定义）
 */
if (!function_exists('writeLog')) {
    function writeLog($level, $message, $context = []) {
        static $logger = null;
        
        if ($logger === null) {
            $logger = new Logger();
        }
        
        $logger->log($level, $message, $context);
    }
}

?>
