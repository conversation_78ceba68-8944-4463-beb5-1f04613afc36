# 通用安全框架部署说明

## 🎯 项目概述

本项目是为好课在线自动化下载器开发的通用安全框架，提供在线授权验证、设备管理、反调试保护等功能。

## 📋 已完成的开发内容

### ✅ 第一阶段：基础框架 (已完成)

1. **数据库设计**
   - ✅ 完整的表结构设计 (`database_init.sql`)
   - ✅ 包含测试数据和初始配置

2. **PHP核心类库**
   - ✅ 配置管理 (`includes/config.php`)
   - ✅ 数据库操作 (`includes/database.php`)
   - ✅ 安全工具 (`includes/security.php`)
   - ✅ 授权管理 (`includes/license.php`)
   - ✅ 日志记录 (`includes/logger.php`)

3. **API接口**
   - ✅ 在线验证接口 (`api/auth/verify.php`)
   - ✅ 完整的错误处理和日志记录

4. **Python客户端SDK**
   - ✅ 核心安全客户端 (`security_client.py`)
   - ✅ 设备指纹生成
   - ✅ 反调试检测
   - ✅ 在线验证功能

5. **项目集成**
   - ✅ 好课在线项目集成示例
   - ✅ 配置文件更新
   - ✅ 兼容模式支持

6. **服务器配置**
   - ✅ Apache配置文件 (`.htaccess`)
   - ✅ 安全防护规则

## 🚀 部署步骤

### 1. 服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装LAMP环境
sudo apt install apache2 mysql-server php7.4 php7.4-mysql php7.4-curl php7.4-json -y

# 启用Apache模块
sudo a2enmod rewrite headers deflate expires
sudo systemctl restart apache2
```

### 2. 数据库初始化

```bash
# 连接MySQL
mysql -u haoke_kechengmao -p

# 执行初始化脚本
source database_init.sql
```

**默认账户信息：**
- 管理员用户名: `admin`
- 管理员密码: `admin123456`
- 测试授权码: `test_license_key_for_development_only_2024`

### 3. 代码部署

```bash
# 创建项目目录
sudo mkdir -p /var/www/haoke.kechengmao.top

# 上传所有文件到服务器
# 包括：includes/, api/, admin/, assets/, .htaccess

# 设置权限
sudo chown -R www-data:www-data /var/www/haoke.kechengmao.top
sudo chmod -R 755 /var/www/haoke.kechengmao.top

# 创建日志目录
sudo mkdir -p /var/www/haoke.kechengmao.top/logs
sudo chmod 777 /var/www/haoke.kechengmao.top/logs
```

### 4. Apache虚拟主机配置

```apache
# /etc/apache2/sites-available/haoke.kechengmao.top.conf
<VirtualHost *:80>
    ServerName haoke.kechengmao.top
    DocumentRoot /var/www/haoke.kechengmao.top
    
    <Directory /var/www/haoke.kechengmao.top>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/haoke_error.log
    CustomLog ${APACHE_LOG_DIR}/haoke_access.log combined
</VirtualHost>

# 启用站点
sudo a2ensite haoke.kechengmao.top.conf
sudo systemctl reload apache2
```

### 5. 客户端集成

在好课在线项目中：

1. **复制SDK文件**
   ```bash
   cp security_client.py /path/to/haoke/project/
   ```

2. **安装依赖**
   ```bash
   pip install requests psutil
   ```

3. **更新配置文件**
   在 `config.json` 中添加：
   ```json
   {
     "license_key": "您的授权码",
     "app_version": "1.0.0"
   }
   ```

## 🔧 配置说明

### 重要配置项

1. **数据库连接** (`includes/config.php`)
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'haoke_kechengmao');
   define('DB_USER', 'haoke_kechengmao');
   define('DB_PASS', '9Fyc8e9HRXbDbzmX');
   ```

2. **安全密钥** (`includes/config.php`)
   ```php
   define('API_SECRET_KEY', 'your-secret-key');
   define('ENCRYPTION_KEY', 'your-encryption-key');
   ```

3. **调试模式** (`includes/config.php`)
   ```php
   define('DEBUG_MODE', false); // 生产环境设为false
   ```

## 🧪 测试验证

### 1. API接口测试

```bash
# 测试验证接口
curl -X POST http://haoke.kechengmao.top/api/auth/verify.php \
  -H "Content-Type: application/json" \
  -d '{
    "app_id": "haoke_downloader",
    "license_key": "test_license_key_for_development_only_2024",
    "device_id": "test_device_12345",
    "timestamp": '$(date +%s)'
  }'
```

### 2. 客户端测试

```python
# 测试Python SDK
python security_client.py
```

### 3. 集成测试

```python
# 运行好课在线项目
python auto_downloader.py
```

## 🔒 安全特性

### 已实现的安全功能

1. **在线验证**
   - 实时授权检查
   - 设备绑定限制
   - 过期时间控制

2. **反调试保护**
   - 调试器检测
   - 虚拟机检测
   - 时间异常检测
   - 进程监控

3. **通信安全**
   - HMAC-SHA256签名
   - 时间戳防重放
   - 请求频率限制

4. **数据保护**
   - 敏感信息加密
   - SQL注入防护
   - XSS攻击防护

## 📊 管理功能

### 管理后台访问

- URL: `http://haoke.kechengmao.top/admin/`
- 用户名: `admin`
- 密码: `admin123456`

### 主要功能

1. **授权管理**
   - 生成授权码
   - 查看授权状态
   - 设备管理

2. **数据统计**
   - 验证次数统计
   - 用户活跃度
   - 错误日志分析

3. **系统监控**
   - 实时日志查看
   - 性能监控
   - 安全事件告警

## 🔄 下一步开发计划

### 第二阶段：安全增强 (待开发)
- [ ] 心跳检测接口
- [ ] 设备注册接口
- [ ] 更高级的反调试保护

### 第三阶段：管理后台 (待开发)
- [ ] 完整的管理界面
- [ ] 数据可视化
- [ ] 批量操作功能

### 第四阶段：更新系统 (待开发)
- [ ] 自动更新检查
- [ ] 增量更新支持
- [ ] 版本回滚机制

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认用户权限
   - 验证网络连接

2. **API返回500错误**
   - 查看PHP错误日志
   - 检查文件权限
   - 确认Apache配置

3. **客户端验证失败**
   - 检查授权码是否正确
   - 确认网络连接
   - 查看服务端日志

### 日志位置

- **PHP错误日志**: `/var/log/apache2/haoke_error.log`
- **应用日志**: `/var/www/haoke.kechengmao.top/logs/security.log`
- **访问日志**: `/var/log/apache2/haoke_access.log`

## 📞 技术支持

如有问题，请检查：
1. 服务器环境配置
2. 数据库连接状态
3. 文件权限设置
4. 网络连接状况
5. 相关日志文件

---

**注意**: 这是第一阶段的基础框架，已经可以正常使用。后续阶段将继续完善管理后台和高级功能。
