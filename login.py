#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import json
import time
import hashlib
import requests
from pathlib import Path

class ZybBase64:
    """作业帮自定义Base64编码器"""
    def __init__(self):
        self.b64 = "BvDzrt4PE3ykMuGFa92whK1-l5CS0bR8nmH_ciZIedLsUTOfqVgWAXjNJ76QopxY="
    
    def encode(self, s):
        """编码字符串"""
        if isinstance(s, str):
            s = s.encode('utf-8')
        
        data = list(s)
        result = []
        i = 0
        length = len(data)
        
        while i < length:
            chunk = data[i:i+3]
            i += 3
            
            b1 = chunk[0] >> 2 if len(chunk) > 0 else 0
            b2 = ((chunk[0] & 0x3) << 4) | (chunk[1] >> 4 if len(chunk) > 1 else 0)
            b3 = ((chunk[1] & 0xF) << 2) | (chunk[2] >> 6 if len(chunk) > 2 else 0) if len(chunk) > 1 else 64
            b4 = chunk[2] & 0x3F if len(chunk) > 2 else 64
            
            if len(chunk) < 2:
                b3 = b4 = 64
            elif len(chunk) < 3:
                b4 = 64
            
            encoded = [
                self.b64[b1],
                self.b64[b2],
                self.b64[b3] if b3 != 64 else '=',
                self.b64[b4] if b4 != 64 else '='
            ]
            
            result.extend(encoded)
        
        return ''.join(result)

class HaokeAuth:
    """
    好课在线认证管理类，负责API登录和cookies处理
    """
    
    @staticmethod
    def _api_login_request(phone, password):
        """
        通过API请求获取作业帮登录凭证zybuss
        
        Args:
            phone: 手机号
            password: 密码
            
        Returns:
            成功返回zybuss字符串，失败返回None
        """
        try:
            # 初始化编码器
            encoder = ZybBase64()
            
            # 加密手机号 - 自定义Base64
            encrypted_phone = encoder.encode(phone)
            
            # 加密密码 - 双重MD5
            first_md5 = hashlib.md5(password.encode('utf-8')).hexdigest()
            encrypted_password = hashlib.md5(first_md5.encode('utf-8')).hexdigest()
            
            # APP端登录参数
            params = {
                'os': 'android',
                'appId': 'homework', 
                'plat': 'android',
                'cType': 'android',
                'phone': encrypted_phone,
                'password': encrypted_password,
                'spamType': '0',
                '_t_': str(int(time.time() * 1000))
            }
            
            # APP端请求头
            headers = {
                'user-agent': 'homework/8.13.0 (Android 10; SM-G975F)',
                'content-type': 'application/x-www-form-urlencoded',
            }
            
            # 发送登录请求
            url = "https://passport.zybang.com/session/pc/login"
            response = requests.post(url, data=params, params={'ajax': ''}, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errNo') == 0 and 'data' in result and 'zybuss' in result['data']:
                    return result['data']['zybuss']
                else:
                    print(f"登录失败: {result.get('errstr', '未知错误')}")
                    return None
            else:
                print(f"请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"登录异常: {str(e)}")
            return None

    @staticmethod
    def auto_login(username, password, cookies_path):
        """
        自动登录，无交互
        
        Args:
            username: 用户名/手机号
            password: 密码
            cookies_path: cookies保存路径
            
        Returns:
            是否登录成功
        """
        # 检查并创建cookies目录
        cookies_dir = os.path.dirname(cookies_path)
        if not os.path.exists(cookies_dir):
            os.makedirs(cookies_dir)
        
        print(f"正在自动登录用户: {username}")
        
        try:
            # 调用API登录获取zybuss
            zybuss_value = HaokeAuth._api_login_request(username, password)
            
            if not zybuss_value:
                print("自动登录失败，请检查账号密码或网络连接")
                return False
            
            # 构造cookies列表格式（用于保存和上报兼容性）
            cookies = [{'name': 'ZYBUSS', 'value': zybuss_value}]
            
            # 保存cookies
            HaokeAuth.save_cookies(cookies, cookies_path)
            
            print("自动登录成功!")
            return True
            
        except Exception as e:
            print(f"自动登录过程发生错误: {e}")
            return False
    
    @staticmethod
    def save_cookies(cookies, path):
        """
        保存cookies到文件，只保存ZYBUSS值
        
        Args:
            cookies: cookies列表
            path: 保存路径
        """
        # 从cookies中提取ZYBUSS值
        zybuss_value = None
        for cookie in cookies:
            if cookie.get('name').upper() == 'ZYBUSS':
                zybuss_value = cookie.get('value')
                break
        
        if not zybuss_value:
            print("警告: 未找到ZYBUSS cookie值")
            return
        
        # 构造简单的cookie字符串
        cookie_string = f"ZYBUSS={zybuss_value}"
        
        # 保存为文本文件
        with open(path, 'w', encoding='utf-8') as f:
            f.write(cookie_string)
        
        print(f"Cookie已保存到: {path}")
    
    @staticmethod
    def load_cookies(path):
        """
        从文件加载cookies
        
        Args:
            path: cookies文件路径
            
        Returns:
            成功返回cookies字典，失败返回None
        """
        if not os.path.exists(path):
            return None
        
        try:
            # 检查文件扩展名，决定如何加载
            if path.endswith('.txt'):
                # 加载简单文本格式
                with open(path, 'r', encoding='utf-8') as f:
                    cookie_string = f.read().strip()
                
                # 解析简单格式为字典（适用于好课在线平台代码）
                cookies = {}
                # 如果以ZYBUSS=开头，特殊处理
                if cookie_string.startswith('ZYBUSS='):
                    cookies['ZYBUSS'] = cookie_string[7:]
                else:
                    # 常规处理多个cookie
                    for item in cookie_string.split(';'):
                        if '=' in item:
                            key, value = item.strip().split('=', 1)
                            cookies[key.strip()] = value.strip()
                
                return cookies
            else:
                # 加载JSON格式并转换为字典
                with open(path, 'r', encoding='utf-8') as f:
                    cookies_list = json.load(f)
                
                if not cookies_list or len(cookies_list) == 0:
                    print("保存的cookies为空")
                    return None
                
                # 将列表格式转换为字典格式
                cookies_dict = {}
                for cookie in cookies_list:
                    cookies_dict[cookie.get('name')] = cookie.get('value')
                    
                return cookies_dict
        except Exception as e:
            print(f"加载cookies失败: {e}")
            return None
    
    @staticmethod
    def validate_cookies(cookies_path):
        """
        验证cookies是否有效
        
        Args:
            cookies_path: cookies文件路径
            
        Returns:
            cookies字典如果有效，否则返回None
        """
        if not os.path.exists(cookies_path):
            return None
        
        cookies = HaokeAuth.load_cookies(cookies_path)
        if cookies and 'ZYBUSS' in cookies:
            print("发现有效的已保存cookies")
            return cookies
        
        return None 