<?php
/**
 * 通用安全框架 - 数据库类
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 防止直接访问
if (!defined('SECURITY_FRAMEWORK')) {
    die('Direct access denied');
}

/**
 * 数据库管理类
 * 使用单例模式，提供PDO数据库操作封装
 */
class Database {
    private $connection;
    private static $instance = null;
    private $transactionLevel = 0;
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct() {
        $this->connect();
    }
    
    /**
     * 获取数据库实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                DB_HOST,
                DB_NAME,
                DB_CHARSET
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_CHARSET . "_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // 设置SQL模式
            $this->connection->exec("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
            
        } catch (PDOException $e) {
            writeLog('ERROR', 'Database connection failed', [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new Exception("数据库连接失败，请稍后重试");
        }
    }
    
    /**
     * 获取PDO连接对象
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * 执行SQL查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            
            // 记录SQL查询（调试模式）
            if (DEBUG_MODE) {
                writeLog('DEBUG', 'SQL Query', [
                    'sql' => $sql,
                    'params' => $params
                ]);
            }
            
            $stmt->execute($params);
            return $stmt;
            
        } catch (PDOException $e) {
            writeLog('ERROR', 'Database query failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw new Exception("数据库查询失败: " . $e->getMessage());
        }
    }
    
    /**
     * 查询单条记录
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 查询多条记录
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 查询单个值
     */
    public function fetchColumn($sql, $params = [], $column = 0) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn($column);
    }
    
    /**
     * 插入数据
     */
    public function insert($table, $data) {
        // 过滤空值和特殊字段
        $data = $this->filterData($data);
        
        if (empty($data)) {
            throw new Exception("插入数据不能为空");
        }
        
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        
        $sql = sprintf(
            "INSERT INTO `%s` (`%s`) VALUES (%s)",
            $table,
            implode('`, `', $fields),
            $placeholders
        );
        
        $stmt = $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }
    
    /**
     * 更新数据
     */
    public function update($table, $data, $where, $whereParams = []) {
        if (empty($data)) {
            throw new Exception("更新数据不能为空");
        }

        $setClause = [];
        $params = [];

        foreach ($data as $field => $value) {
            if (empty($field)) {
                continue;
            }

            // 处理PDOExpression
            if ($value instanceof PDOExpression) {
                $setClause[] = "`{$field}` = " . $value->getValue();
            } else {
                $setClause[] = "`{$field}` = :{$field}";
                // 处理特殊值
                if ($value === null) {
                    $params[$field] = null;
                } elseif (is_bool($value)) {
                    $params[$field] = $value ? 1 : 0;
                } else {
                    $params[$field] = $value;
                }
            }
        }

        if (empty($setClause)) {
            throw new Exception("更新数据不能为空");
        }

        $sql = sprintf(
            "UPDATE `%s` SET %s WHERE %s",
            $table,
            implode(', ', $setClause),
            $where
        );

        // 合并参数，确保命名参数在前，位置参数在后
        $allParams = $params;
        foreach ($whereParams as $param) {
            $allParams[] = $param;
        }
        $stmt = $this->query($sql, $allParams);

        return $stmt->rowCount();
    }
    
    /**
     * 删除数据
     */
    public function delete($table, $where, $params = []) {
        $sql = sprintf("DELETE FROM `%s` WHERE %s", $table, $where);
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        if ($this->transactionLevel === 0) {
            $this->connection->beginTransaction();
        }
        $this->transactionLevel++;
        
        writeLog('DEBUG', 'Transaction started', ['level' => $this->transactionLevel]);
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        if ($this->transactionLevel === 1) {
            $this->connection->commit();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        
        writeLog('DEBUG', 'Transaction committed', ['level' => $this->transactionLevel]);
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        if ($this->transactionLevel === 1) {
            $this->connection->rollback();
        }
        $this->transactionLevel = max(0, $this->transactionLevel - 1);
        
        writeLog('DEBUG', 'Transaction rolled back', ['level' => $this->transactionLevel]);
    }
    
    /**
     * 执行事务
     */
    public function transaction($callback) {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
    
    /**
     * 检查表是否存在
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchOne($sql, [$tableName]);
        return !empty($result);
    }
    
    /**
     * 获取表结构
     */
    public function getTableStructure($tableName) {
        $sql = "DESCRIBE `{$tableName}`";
        return $this->fetchAll($sql);
    }
    
    /**
     * 过滤数据
     */
    private function filterData($data) {
        $filtered = [];
        
        foreach ($data as $key => $value) {
            // 跳过空键名
            if (empty($key)) {
                continue;
            }
            
            // 处理特殊值
            if ($value instanceof PDOExpression) {
                // 原生SQL表达式
                $filtered[$key] = $value->getValue();
            } elseif ($value === null) {
                $filtered[$key] = null;
            } elseif (is_bool($value)) {
                $filtered[$key] = $value ? 1 : 0;
            } else {
                $filtered[$key] = $value;
            }
        }
        
        return $filtered;
    }
    
    /**
     * 获取数据库状态
     */
    public function getStatus() {
        try {
            $status = [
                'connected' => $this->connection !== null,
                'server_info' => $this->connection->getAttribute(PDO::ATTR_SERVER_INFO),
                'client_version' => $this->connection->getAttribute(PDO::ATTR_CLIENT_VERSION),
                'server_version' => $this->connection->getAttribute(PDO::ATTR_SERVER_VERSION),
                'connection_status' => $this->connection->getAttribute(PDO::ATTR_CONNECTION_STATUS)
            ];
            
            return $status;
        } catch (Exception $e) {
            return ['connected' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * PDO表达式类，用于原生SQL表达式
 */
class PDOExpression {
    private $expression;
    
    public function __construct($expression) {
        $this->expression = $expression;
    }
    
    public function getValue() {
        return $this->expression;
    }
    
    public function __toString() {
        return $this->expression;
    }
}

?>
