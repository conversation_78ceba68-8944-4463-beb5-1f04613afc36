<?php
/**
 * 通用安全框架 - 配置文件
 * 
 * <AUTHOR> Framework Team
 * @version 1.0.0
 * @date 2024-08-08
 */

// 防止直接访问
if (!defined('SECURITY_FRAMEWORK')) {
    define('SECURITY_FRAMEWORK', true);
}

// ============================================================================
// 数据库配置
// ============================================================================
define('DB_HOST', 'localhost');
define('DB_NAME', 'haoke_kechengmao');
define('DB_USER', 'haoke_kechengmao');
define('DB_PASS', '9Fyc8e9HRXbDbzmX');
define('DB_CHARSET', 'utf8mb4');

// ============================================================================
// 安全配置
// ============================================================================
define('API_SECRET_KEY', 'hk_api_secret_2024_ultra_secure_key_for_signature_verification');
define('ENCRYPTION_KEY', 'hk_encrypt_2024_32char_key_here'); // 32字符加密密钥
define('JWT_SECRET', 'hk_jwt_secret_2024_for_admin_authentication_system');

// ============================================================================
// 系统配置
// ============================================================================
define('SYSTEM_TIMEZONE', 'Asia/Shanghai');
define('LOG_LEVEL', 'INFO');
define('MAX_DEVICES_PER_LICENSE', 3);
define('VERIFY_TIMEOUT', 30); // 验证超时时间(秒)
define('DEBUG_MODE', false); // 生产环境设为false

// ============================================================================
// API配置
// ============================================================================
define('API_VERSION', 'v1');
define('RATE_LIMIT_REQUESTS', 100); // 每分钟最大请求数
define('RATE_LIMIT_WINDOW', 60); // 限制窗口(秒)

// ============================================================================
// 路径配置
// ============================================================================
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('API_PATH', ROOT_PATH . '/api');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// ============================================================================
// 安全头设置
// ============================================================================
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('X-Powered-By: Security Framework v1.0');

// 强制HTTPS (生产环境启用)
if (!DEBUG_MODE && !isset($_SERVER['HTTPS'])) {
    // header('Location: https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);
    // exit;
}

// ============================================================================
// 错误报告设置
// ============================================================================
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// ============================================================================
// PHP配置优化
// ============================================================================
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 60);
ini_set('post_max_size', '50M');
ini_set('upload_max_filesize', '50M');

// 时区设置
date_default_timezone_set(SYSTEM_TIMEZONE);

// ============================================================================
// 自动加载函数
// ============================================================================
spl_autoload_register(function ($className) {
    $classFile = INCLUDES_PATH . '/' . strtolower($className) . '.php';
    if (file_exists($classFile)) {
        require_once $classFile;
    }
});

// ============================================================================
// 全局函数
// ============================================================================

/**
 * 获取配置值
 */
function getConfig($key, $default = null) {
    static $configs = null;
    
    if ($configs === null) {
        try {
            $db = Database::getInstance();
            $result = $db->fetchAll("SELECT config_key, config_value, config_type FROM system_config");
            
            $configs = [];
            foreach ($result as $row) {
                $value = $row['config_value'];
                
                // 根据类型转换值
                switch ($row['config_type']) {
                    case 'int':
                        $value = (int)$value;
                        break;
                    case 'bool':
                        $value = (bool)$value;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                $configs[$row['config_key']] = $value;
            }
        } catch (Exception $e) {
            $configs = [];
        }
    }
    
    return isset($configs[$key]) ? $configs[$key] : $default;
}

/**
 * 记录日志
 */
function writeLog($level, $message, $context = []) {
    try {
        $logger = new Logger();
        $logger->log($level, $message, $context);
    } catch (Exception $e) {
        error_log("Log write failed: " . $e->getMessage());
    }
}

/**
 * 获取客户端IP
 */
function getClientIP() {
    $ipKeys = [
        'HTTP_CF_CONNECTING_IP',     // Cloudflare
        'HTTP_X_FORWARDED_FOR',      // 代理
        'HTTP_X_REAL_IP',            // Nginx
        'HTTP_CLIENT_IP',            // 代理
        'REMOTE_ADDR'                // 标准
    ];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            // 验证IP格式
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * 安全的JSON响应
 */
function jsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    header('Content-Type: application/json; charset=utf-8');
    
    $response = [
        'timestamp' => time(),
        'success' => $httpCode >= 200 && $httpCode < 300,
    ];
    
    if (is_array($data)) {
        $response = array_merge($response, $data);
    } else {
        $response['message'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 错误响应
 */
function errorResponse($message, $code = 500, $errorCode = null) {
    $data = ['message' => $message];
    if ($errorCode) {
        $data['error_code'] = $errorCode;
    }
    jsonResponse($data, $code);
}

/**
 * 成功响应
 */
function successResponse($data = [], $message = 'Success') {
    if (is_string($data)) {
        $message = $data;
        $data = [];
    }
    
    $response = ['message' => $message];
    if (!empty($data)) {
        $response['data'] = $data;
    }
    
    jsonResponse($response, 200);
}

// ============================================================================
// 初始化检查
// ============================================================================

// 检查必要目录
$requiredDirs = [LOGS_PATH, UPLOADS_PATH];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 检查日志文件权限
$logFile = LOGS_PATH . '/security.log';
if (!is_writable(dirname($logFile))) {
    error_log("Warning: Log directory is not writable: " . dirname($logFile));
}

// 记录系统启动
if (DEBUG_MODE) {
    writeLog('INFO', 'Security Framework initialized', [
        'version' => API_VERSION,
        'debug_mode' => DEBUG_MODE,
        'client_ip' => getClientIP(),
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
}

?>
