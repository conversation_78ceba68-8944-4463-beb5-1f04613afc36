-- 通用安全框架数据库初始化脚本
-- 数据库: haoke_kechengmao
-- 用户: haoke_kechengmao
-- 密码: 9Fyc8e9HRXbDbzmX

-- 使用数据库
USE haoke_kechengmao;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. 应用管理表 (apps)
-- ============================================================================
DROP TABLE IF EXISTS `apps`;
CREATE TABLE `apps` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `app_name` varchar(100) NOT NULL COMMENT '应用名称',
    `app_secret` varchar(64) NOT NULL COMMENT '应用密钥',
    `version` varchar(20) DEFAULT '1.0.0' COMMENT '当前版本',
    `download_url` text COMMENT '下载地址',
    `update_message` text COMMENT '更新说明',
    `force_update` tinyint(1) DEFAULT 0 COMMENT '强制更新',
    `min_version` varchar(20) DEFAULT NULL COMMENT '最低兼容版本',
    `status` tinyint(1) DEFAULT 1 COMMENT '应用状态',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用管理表';

-- ============================================================================
-- 2. 授权许可表 (licenses)
-- ============================================================================
DROP TABLE IF EXISTS `licenses`;
CREATE TABLE `licenses` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `license_key` varchar(64) NOT NULL COMMENT '授权码',
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
    `user_contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
    `license_type` enum('trial','daily','weekly','monthly','yearly','permanent') DEFAULT 'trial' COMMENT '授权类型',
    `expire_date` datetime DEFAULT NULL COMMENT '过期时间',
    `max_devices` int(11) DEFAULT 1 COMMENT '最大设备数',
    `used_devices` int(11) DEFAULT 0 COMMENT '已使用设备数',
    `total_verifications` int(11) DEFAULT 0 COMMENT '总验证次数',
    `last_verify_time` timestamp NULL DEFAULT NULL COMMENT '最后验证时间',
    `last_verify_ip` varchar(45) DEFAULT NULL COMMENT '最后验证IP',
    `status` enum('active','expired','banned','pending') DEFAULT 'pending' COMMENT '状态',
    `notes` text COMMENT '备注信息',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `license_key` (`license_key`),
    KEY `app_id` (`app_id`),
    KEY `status` (`status`),
    KEY `expire_date` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权许可表';

-- ============================================================================
-- 3. 设备管理表 (devices)
-- ============================================================================
DROP TABLE IF EXISTS `devices`;
CREATE TABLE `devices` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` varchar(128) NOT NULL COMMENT '设备唯一ID',
    `license_id` int(11) NOT NULL COMMENT '授权ID',
    `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
    `os_info` varchar(200) DEFAULT NULL COMMENT '操作系统信息',
    `hardware_info` text COMMENT '硬件信息JSON',
    `first_ip` varchar(45) DEFAULT NULL COMMENT '首次注册IP',
    `last_ip` varchar(45) DEFAULT NULL COMMENT '最后使用IP',
    `first_login` timestamp NULL DEFAULT NULL COMMENT '首次登录时间',
    `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
    `status` enum('active','banned','suspicious') DEFAULT 'active' COMMENT '设备状态',
    `risk_score` int(11) DEFAULT 0 COMMENT '风险评分',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `device_license` (`device_id`, `license_id`),
    KEY `license_id` (`license_id`),
    KEY `device_id` (`device_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备管理表';

-- ============================================================================
-- 4. 验证日志表 (verify_logs)
-- ============================================================================
DROP TABLE IF EXISTS `verify_logs`;
CREATE TABLE `verify_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `license_key` varchar(64) NOT NULL COMMENT '授权码',
    `device_id` varchar(128) NOT NULL COMMENT '设备ID',
    `app_id` varchar(50) NOT NULL COMMENT '应用ID',
    `client_version` varchar(20) DEFAULT NULL COMMENT '客户端版本',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `request_data` text COMMENT '请求数据JSON',
    `verify_result` enum('success','failed','banned','expired','invalid') NOT NULL COMMENT '验证结果',
    `error_code` varchar(20) DEFAULT NULL COMMENT '错误代码',
    `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
    `response_time` int(11) DEFAULT NULL COMMENT '响应时间(ms)',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `license_key` (`license_key`),
    KEY `device_id` (`device_id`),
    KEY `app_id` (`app_id`),
    KEY `verify_result` (`verify_result`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证日志表';

-- ============================================================================
-- 5. 系统配置表 (system_config)
-- ============================================================================
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `config_type` enum('string','int','bool','json') DEFAULT 'string' COMMENT '配置类型',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开配置',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ============================================================================
-- 6. 管理员表 (admins)
-- ============================================================================
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(255) NOT NULL COMMENT '密码哈希',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `role` enum('super','admin','operator') DEFAULT 'operator' COMMENT '角色',
    `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
    `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ============================================================================
-- 初始化数据
-- ============================================================================

-- 插入默认应用
INSERT INTO `apps` (`app_id`, `app_name`, `app_secret`, `version`, `status`) VALUES
('haoke_downloader', '好课在线下载器', 'hk_secret_2024_secure_key_for_haoke_downloader_app', '1.0.0', 1);

-- 插入默认管理员账户 (用户名: admin, 密码: admin123456)
INSERT INTO `admins` (`username`, `password`, `email`, `role`, `status`) VALUES
('admin', '$argon2id$v=19$m=65536,t=4,p=3$YWRtaW4xMjM0NTY$rQx8kZwsNhAr7NOQKWJrZQxNxZQxNxZQxNxZQxNxZQ', '<EMAIL>', 'super', 1);

-- 插入系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `description`, `is_public`) VALUES
('site_name', '好课在线安全框架', 'string', '网站名称', 1),
('max_devices_per_license', '3', 'int', '每个授权最大设备数', 0),
('verify_timeout', '30', 'int', '验证超时时间(秒)', 0),
('rate_limit_requests', '100', 'int', '每分钟最大请求数', 0),
('rate_limit_window', '60', 'int', '限制窗口(秒)', 0),
('enable_debug', '0', 'bool', '启用调试模式', 0),
('maintenance_mode', '0', 'bool', '维护模式', 1);

-- 插入测试授权码 (仅用于开发测试)
INSERT INTO `licenses` (`license_key`, `app_id`, `user_name`, `user_contact`, `license_type`, `expire_date`, `max_devices`, `status`, `notes`) VALUES
('test_license_key_for_development_only_2024', 'haoke_downloader', '测试用户', '<EMAIL>', 'monthly', DATE_ADD(NOW(), INTERVAL 30 DAY), 2, 'active', '开发测试用授权码');

-- 设置外键约束
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as Status;
SELECT COUNT(*) as 'Total Apps' FROM apps;
SELECT COUNT(*) as 'Total Admins' FROM admins;
SELECT COUNT(*) as 'Total Configs' FROM system_config;
SELECT COUNT(*) as 'Total Test Licenses' FROM licenses;