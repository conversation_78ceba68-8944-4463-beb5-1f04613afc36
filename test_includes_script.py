#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_includes():
    """测试includes文件"""
    print("🔍 测试includes文件...")
    
    try:
        response = requests.get(
            "https://haoke.kechengmao.top/test_includes.php",
            timeout=15
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ includes测试成功")
                print(f"测试结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
            except:
                print("❌ 响应不是有效JSON")
                print(f"响应内容: {response.text}")
        else:
            print(f"❌ includes测试失败")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ includes测试异常: {e}")

if __name__ == "__main__":
    test_includes()
