<?php
/**
 * 简化版验证接口 - 用于测试
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求',
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
    exit;
}

try {
    // 获取请求数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);
    
    // 如果JSON解析失败，尝试解析表单数据
    if ($input === null) {
        $input = $_POST;
    }
    
    if (empty($input)) {
        throw new Exception('请求数据为空');
    }
    
    // 验证必需参数
    $requiredParams = ['app_id', 'license_key', 'device_id', 'timestamp'];
    $missingParams = [];
    
    foreach ($requiredParams as $param) {
        if (!isset($input[$param]) || $input[$param] === '') {
            $missingParams[] = $param;
        }
    }
    
    if (!empty($missingParams)) {
        throw new Exception('缺少必需参数: ' . implode(', ', $missingParams));
    }
    
    // 简单的测试验证逻辑
    $testLicenseKey = 'test_license_key_for_development_only_2024';
    
    if ($input['license_key'] === $testLicenseKey) {
        // 验证成功
        echo json_encode([
            'success' => true,
            'message' => '验证成功（简化版测试）',
            'data' => [
                'license_type' => 'monthly',
                'expire_date' => date('Y-m-d H:i:s', time() + 30 * 24 * 60 * 60),
                'user_name' => '测试用户',
                'max_devices' => 2,
                'used_devices' => 1
            ],
            'server_time' => time(),
            'test_mode' => true
        ]);
    } else {
        // 验证失败
        echo json_encode([
            'success' => false,
            'message' => '授权码无效',
            'error_code' => 'LICENSE_NOT_FOUND',
            'test_mode' => true
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '验证失败: ' . $e->getMessage(),
        'error_code' => 'VERIFICATION_ERROR',
        'test_mode' => true
    ]);
}
?>
