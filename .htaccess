# 通用安全框架 - Apache配置文件
# 
# <AUTHOR> Framework Team
# @version 1.0.0
# @date 2024-08-08

# 启用重写引擎
RewriteEngine On

# 安全配置
# 禁止访问敏感文件
<FilesMatch "\.(log|sql|md|txt|bak|backup|old|tmp)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# 禁止访问配置文件
<FilesMatch "^(config|database|security|license|logger)\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 禁止访问includes目录
<IfModule mod_rewrite.c>
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^uploads/.*\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$ - [F,L]
</IfModule>

# 防止目录浏览
Options -Indexes

# 安全头设置
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# 压缩配置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 缓存配置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
</IfModule>

# API路由重写
<IfModule mod_rewrite.c>
    # API接口路由
    RewriteRule ^api/v1/auth/verify/?$ api/auth/verify.php [L,QSA]
    RewriteRule ^api/v1/auth/activate/?$ api/auth/activate.php [L,QSA]
    RewriteRule ^api/v1/auth/heartbeat/?$ api/auth/heartbeat.php [L,QSA]
    RewriteRule ^api/v1/device/register/?$ api/device/register.php [L,QSA]
    RewriteRule ^api/v1/device/info/?$ api/device/info.php [L,QSA]
    RewriteRule ^api/v1/update/check/?$ api/update/check.php [L,QSA]
    RewriteRule ^api/v1/update/download/?$ api/update/download.php [L,QSA]
    
    # 管理后台路由
    RewriteRule ^admin/?$ admin/index.php [L]
    RewriteRule ^admin/dashboard/?$ admin/dashboard.php [L]
    RewriteRule ^admin/licenses/?$ admin/licenses.php [L]
    RewriteRule ^admin/devices/?$ admin/devices.php [L]
    RewriteRule ^admin/logs/?$ admin/logs.php [L]
    RewriteRule ^admin/settings/?$ admin/settings.php [L]
</IfModule>

# 错误页面
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html

# PHP配置优化
<IfModule mod_php7.c>
    php_value max_execution_time 60
    php_value memory_limit 256M
    php_value post_max_size 50M
    php_value upload_max_filesize 50M
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# 限制请求方法
<LimitExcept GET POST HEAD OPTIONS>
    Order Allow,Deny
    Deny from all
</LimitExcept>

# 防止SQL注入和XSS攻击
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# 防止用户代理攻击
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(-|\.|') [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(.*)(<|>|%3C|%3E)(.*) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^(.*)(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^(.*)(%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^(.*)(;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# 限制文件上传类型
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    <IfModule mod_rewrite.c>
        RewriteEngine On
        RewriteCond %{REQUEST_URI} ^/uploads/
        RewriteRule ^(.*)$ - [F,L]
    </IfModule>
</FilesMatch>
